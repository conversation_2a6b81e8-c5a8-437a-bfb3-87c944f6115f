2025/07/25 04:40:58 [warn] 1#1: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 04:40:58 [notice] 1#1: using the "epoll" event method
2025/07/25 04:40:58 [notice] 1#1: nginx/1.29.0
2025/07/25 04:40:58 [notice] 1#1: built by gcc 14.2.0 (Alpine 14.2.0) 
2025/07/25 04:40:58 [notice] 1#1: OS: Linux 6.1.0-37-cloud-amd64
2025/07/25 04:40:58 [notice] 1#1: getrlimit(RLIMIT_NOFILE): 1048576:1048576
2025/07/25 04:40:58 [notice] 1#1: start worker processes
2025/07/25 04:40:58 [notice] 1#1: start worker process 22
2025/07/25 04:40:58 [notice] 1#1: start worker process 23
2025/07/25 04:41:03 [warn] 24#24: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 04:41:33 [warn] 30#30: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 04:42:03 [warn] 37#37: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 04:42:33 [warn] 44#44: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 04:43:03 [warn] 50#50: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 04:43:33 [warn] 56#56: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 04:44:04 [warn] 62#62: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 04:44:34 [warn] 69#69: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 04:45:04 [warn] 76#76: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 04:45:34 [warn] 83#83: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 04:46:04 [warn] 90#90: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 04:46:34 [warn] 97#97: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 04:47:04 [warn] 104#104: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 04:47:34 [warn] 111#111: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 04:48:04 [warn] 117#117: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 04:48:34 [warn] 123#123: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 04:49:04 [warn] 130#130: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 04:49:07 [notice] 1#1: signal 3 (SIGQUIT) received, shutting down
2025/07/25 04:49:07 [notice] 22#22: gracefully shutting down
2025/07/25 04:49:07 [notice] 22#22: exiting
2025/07/25 04:49:07 [notice] 22#22: exit
2025/07/25 04:49:07 [notice] 23#23: gracefully shutting down
2025/07/25 04:49:07 [notice] 23#23: exiting
2025/07/25 04:49:07 [notice] 23#23: exit
2025/07/25 04:49:07 [notice] 1#1: signal 17 (SIGCHLD) received from 22
2025/07/25 04:49:07 [notice] 1#1: worker process 22 exited with code 0
2025/07/25 04:49:07 [notice] 1#1: signal 29 (SIGIO) received
2025/07/25 04:49:07 [notice] 1#1: signal 17 (SIGCHLD) received from 23
2025/07/25 04:49:07 [notice] 1#1: worker process 23 exited with code 0
2025/07/25 04:49:07 [notice] 1#1: exit
2025/07/25 04:49:08 [warn] 1#1: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 04:49:08 [notice] 1#1: using the "epoll" event method
2025/07/25 04:49:08 [notice] 1#1: nginx/1.29.0
2025/07/25 04:49:08 [notice] 1#1: built by gcc 14.2.0 (Alpine 14.2.0) 
2025/07/25 04:49:08 [notice] 1#1: OS: Linux 6.1.0-37-cloud-amd64
2025/07/25 04:49:08 [notice] 1#1: getrlimit(RLIMIT_NOFILE): 1048576:1048576
2025/07/25 04:49:08 [notice] 1#1: start worker processes
2025/07/25 04:49:08 [notice] 1#1: start worker process 22
2025/07/25 04:49:08 [notice] 1#1: start worker process 23
2025/07/25 04:49:13 [warn] 24#24: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 04:49:43 [warn] 30#30: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 04:50:13 [warn] 36#36: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 04:50:43 [warn] 43#43: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 04:51:13 [warn] 50#50: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 04:51:43 [warn] 56#56: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 04:52:13 [warn] 63#63: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 04:52:44 [warn] 70#70: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 04:53:14 [warn] 76#76: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 04:53:44 [warn] 83#83: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 04:54:14 [warn] 89#89: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 04:54:44 [warn] 96#96: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 04:55:14 [warn] 103#103: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 04:55:44 [warn] 110#110: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 04:56:14 [warn] 116#116: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 04:56:44 [warn] 122#122: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 04:57:14 [warn] 129#129: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 04:57:44 [warn] 136#136: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 04:58:14 [warn] 143#143: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 04:58:44 [warn] 149#149: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 04:59:14 [warn] 156#156: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 04:59:45 [warn] 163#163: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 05:00:15 [warn] 169#169: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 05:00:45 [warn] 175#175: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 05:01:15 [warn] 181#181: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 05:01:45 [warn] 187#187: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 05:02:15 [warn] 193#193: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 05:02:45 [warn] 200#200: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 05:03:15 [warn] 206#206: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 05:03:45 [warn] 212#212: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 05:04:15 [warn] 219#219: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 05:04:45 [warn] 225#225: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 05:05:15 [warn] 232#232: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 05:05:45 [warn] 239#239: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 05:06:16 [warn] 245#245: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 05:06:46 [warn] 252#252: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 05:07:16 [warn] 259#259: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 05:07:46 [warn] 266#266: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 05:08:16 [warn] 273#273: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 05:08:46 [warn] 280#280: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 05:09:16 [warn] 286#286: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 05:09:46 [warn] 292#292: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 05:10:16 [warn] 299#299: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 05:10:46 [warn] 305#305: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 05:11:16 [warn] 311#311: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 05:11:46 [warn] 317#317: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 05:12:16 [warn] 323#323: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 05:12:46 [warn] 329#329: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 05:13:17 [warn] 335#335: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 05:13:47 [warn] 341#341: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 05:14:17 [warn] 347#347: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 05:14:47 [warn] 353#353: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 05:15:17 [warn] 360#360: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 05:15:47 [warn] 366#366: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 05:16:17 [warn] 373#373: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 05:16:47 [warn] 380#380: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 05:17:17 [warn] 386#386: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 05:17:47 [warn] 393#393: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 05:18:17 [warn] 400#400: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 05:18:47 [warn] 407#407: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 05:19:17 [warn] 414#414: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 05:19:47 [warn] 421#421: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 05:20:18 [warn] 427#427: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 05:20:48 [warn] 434#434: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 05:21:18 [warn] 441#441: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 05:21:48 [warn] 447#447: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 05:22:18 [warn] 454#454: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 05:22:48 [warn] 461#461: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 05:23:18 [warn] 467#467: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 05:23:48 [warn] 473#473: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 05:24:18 [warn] 479#479: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 05:24:48 [warn] 485#485: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 05:25:18 [warn] 492#492: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 05:25:48 [warn] 498#498: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 05:26:18 [warn] 504#504: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 05:26:48 [warn] 511#511: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 05:27:19 [warn] 518#518: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 05:27:49 [warn] 525#525: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 05:28:19 [warn] 531#531: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 05:28:49 [warn] 538#538: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 05:29:19 [warn] 545#545: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 05:29:49 [warn] 551#551: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 05:30:19 [warn] 557#557: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 05:30:49 [warn] 564#564: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 05:31:19 [warn] 570#570: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 05:31:49 [warn] 577#577: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 05:32:19 [warn] 585#585: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 05:32:49 [warn] 591#591: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 05:33:19 [warn] 597#597: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 05:33:49 [warn] 604#604: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 05:34:20 [warn] 611#611: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 05:34:50 [warn] 618#618: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 05:35:20 [warn] 625#625: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 05:35:50 [warn] 631#631: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 05:36:20 [warn] 637#637: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 05:36:50 [warn] 644#644: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 05:37:20 [warn] 650#650: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 05:37:50 [warn] 657#657: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 05:38:20 [warn] 663#663: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 05:38:50 [warn] 669#669: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 05:39:20 [warn] 675#675: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 05:39:50 [warn] 681#681: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 05:40:20 [warn] 687#687: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 05:40:51 [warn] 693#693: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 05:41:21 [warn] 699#699: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 05:41:51 [warn] 705#705: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 05:42:21 [warn] 712#712: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 05:42:51 [warn] 718#718: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 05:43:21 [warn] 725#725: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 05:43:51 [warn] 732#732: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 05:44:21 [warn] 738#738: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 05:44:51 [warn] 744#744: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 05:45:21 [warn] 751#751: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 05:45:51 [warn] 758#758: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 05:46:21 [warn] 765#765: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 05:46:51 [warn] 771#771: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 05:47:21 [warn] 777#777: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 05:47:52 [warn] 784#784: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 05:48:22 [warn] 790#790: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 05:48:52 [warn] 797#797: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 05:49:22 [warn] 804#804: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 05:49:52 [warn] 810#810: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 05:50:22 [warn] 816#816: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 05:50:52 [warn] 822#822: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 05:51:22 [warn] 829#829: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 05:51:52 [warn] 836#836: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 05:52:22 [warn] 843#843: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 05:52:52 [warn] 850#850: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 05:53:22 [warn] 856#856: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 05:53:52 [warn] 863#863: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 05:54:22 [warn] 869#869: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 05:54:52 [warn] 876#876: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 05:55:23 [warn] 883#883: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 05:55:53 [warn] 889#889: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 05:56:23 [warn] 895#895: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 05:56:53 [warn] 902#902: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 05:57:23 [warn] 909#909: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 05:57:53 [warn] 915#915: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 05:58:23 [warn] 921#921: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 05:58:53 [warn] 927#927: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 05:59:23 [warn] 934#934: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 05:59:53 [warn] 940#940: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 06:00:23 [warn] 947#947: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 06:00:53 [warn] 954#954: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 06:01:23 [warn] 960#960: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 06:01:53 [warn] 966#966: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 06:02:23 [warn] 973#973: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 06:02:54 [warn] 979#979: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 06:03:24 [warn] 986#986: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 06:03:54 [warn] 994#994: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 06:04:24 [warn] 1001#1001: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 06:04:54 [warn] 1008#1008: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 06:05:24 [warn] 1015#1015: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 06:05:54 [warn] 1022#1022: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 06:06:24 [warn] 1029#1029: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 06:06:54 [warn] 1035#1035: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 06:07:24 [warn] 1042#1042: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 06:07:54 [warn] 1048#1048: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 06:08:24 [warn] 1054#1054: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 06:08:54 [warn] 1060#1060: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 06:09:24 [warn] 1067#1067: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 06:09:55 [warn] 1073#1073: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 06:10:25 [warn] 1080#1080: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 06:10:55 [warn] 1087#1087: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 06:11:25 [warn] 1093#1093: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 06:11:55 [warn] 1100#1100: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 06:12:25 [warn] 1106#1106: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 06:12:55 [warn] 1113#1113: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 06:13:25 [warn] 1120#1120: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 06:13:55 [warn] 1126#1126: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 06:14:25 [warn] 1133#1133: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 06:14:55 [warn] 1140#1140: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 06:15:25 [warn] 1146#1146: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 06:15:55 [warn] 1152#1152: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 06:16:25 [warn] 1159#1159: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 06:16:56 [warn] 1166#1166: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 06:17:26 [warn] 1173#1173: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 06:17:56 [warn] 1180#1180: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 06:18:26 [warn] 1187#1187: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 06:18:56 [warn] 1193#1193: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 06:19:26 [warn] 1200#1200: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 06:19:56 [warn] 1206#1206: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 06:20:26 [warn] 1212#1212: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 06:20:56 [warn] 1218#1218: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 06:21:26 [warn] 1225#1225: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 06:21:56 [warn] 1231#1231: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 06:22:26 [warn] 1237#1237: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 06:22:56 [warn] 1243#1243: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 06:23:26 [warn] 1249#1249: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 06:23:57 [warn] 1255#1255: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 06:24:27 [warn] 1262#1262: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 06:24:57 [warn] 1268#1268: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 06:25:27 [warn] 1274#1274: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 06:25:57 [warn] 1280#1280: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 06:26:27 [warn] 1287#1287: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 06:26:57 [warn] 1294#1294: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 06:27:27 [warn] 1300#1300: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 06:27:57 [warn] 1307#1307: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 06:28:27 [warn] 1313#1313: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 06:28:57 [warn] 1320#1320: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 06:29:27 [warn] 1326#1326: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 06:29:57 [warn] 1333#1333: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 06:30:27 [warn] 1345#1345: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 06:30:57 [warn] 1351#1351: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 06:31:28 [warn] 1358#1358: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 06:31:58 [warn] 1365#1365: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 06:32:28 [warn] 1372#1372: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 06:32:58 [warn] 1379#1379: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 06:33:28 [warn] 1386#1386: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 06:33:58 [warn] 1393#1393: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 06:34:28 [warn] 1399#1399: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 06:34:58 [warn] 1405#1405: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 06:35:28 [warn] 1412#1412: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 06:35:58 [warn] 1418#1418: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 06:36:28 [warn] 1425#1425: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 06:36:58 [warn] 1432#1432: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 06:37:28 [warn] 1438#1438: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 06:37:58 [warn] 1445#1445: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 06:38:28 [warn] 1452#1452: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 06:38:59 [warn] 1458#1458: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 06:39:29 [warn] 1464#1464: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 06:39:59 [warn] 1470#1470: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 06:40:29 [warn] 1476#1476: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 06:40:59 [warn] 1482#1482: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 06:41:29 [warn] 1489#1489: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 06:41:59 [warn] 1496#1496: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 06:42:29 [warn] 1503#1503: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 06:42:59 [warn] 1509#1509: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 06:43:29 [warn] 1516#1516: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 06:43:59 [warn] 1522#1522: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 06:44:29 [warn] 1529#1529: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 06:44:59 [warn] 1536#1536: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 06:45:29 [warn] 1543#1543: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 06:45:59 [warn] 1549#1549: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 06:46:30 [warn] 1555#1555: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 06:47:00 [warn] 1561#1561: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 06:47:30 [warn] 1567#1567: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 06:48:00 [warn] 1574#1574: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 06:48:30 [warn] 1580#1580: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 06:49:00 [warn] 1586#1586: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 06:49:30 [warn] 1592#1592: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 06:50:00 [warn] 1598#1598: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 06:50:30 [warn] 1603#1603: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 06:51:00 [warn] 1609#1609: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 06:51:30 [warn] 1615#1615: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 06:52:00 [warn] 1621#1621: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 06:52:30 [warn] 1628#1628: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 06:53:00 [warn] 1634#1634: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 06:53:31 [warn] 1640#1640: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 06:54:01 [warn] 1646#1646: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 06:54:31 [warn] 1652#1652: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 06:55:01 [warn] 1659#1659: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 06:55:31 [warn] 1666#1666: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 06:56:01 [warn] 1672#1672: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 06:56:31 [warn] 1679#1679: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 06:57:01 [warn] 1685#1685: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 06:57:31 [warn] 1692#1692: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 06:58:01 [warn] 1699#1699: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 06:58:31 [warn] 1706#1706: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 06:59:01 [warn] 1713#1713: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 06:59:31 [warn] 1720#1720: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 07:00:01 [warn] 1727#1727: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 07:00:32 [warn] 1733#1733: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 07:01:02 [warn] 1740#1740: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 07:01:32 [warn] 1747#1747: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 07:02:02 [warn] 1754#1754: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 07:02:32 [warn] 1761#1761: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 07:03:02 [warn] 1768#1768: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 07:03:32 [warn] 1774#1774: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 07:04:02 [warn] 1781#1781: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 07:04:32 [warn] 1788#1788: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 07:05:02 [warn] 1795#1795: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 07:05:32 [warn] 1802#1802: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 07:06:02 [warn] 1808#1808: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 07:06:32 [warn] 1815#1815: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 07:07:02 [warn] 1821#1821: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 07:07:33 [warn] 1828#1828: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 07:08:03 [warn] 1834#1834: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 07:08:33 [warn] 1841#1841: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 07:09:03 [warn] 1848#1848: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 07:09:33 [warn] 1855#1855: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 07:10:03 [warn] 1862#1862: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 07:10:33 [warn] 1869#1869: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 07:11:03 [warn] 1876#1876: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 07:11:33 [warn] 1883#1883: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 07:12:03 [warn] 1889#1889: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 07:12:33 [warn] 1895#1895: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 07:13:03 [warn] 1902#1902: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 07:13:33 [warn] 1908#1908: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 07:14:03 [warn] 1915#1915: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 07:14:34 [warn] 1922#1922: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 07:15:04 [warn] 1929#1929: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 07:15:34 [warn] 1935#1935: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 07:16:04 [warn] 1941#1941: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 07:16:34 [warn] 1947#1947: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 07:17:04 [warn] 1953#1953: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 07:17:34 [warn] 1959#1959: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 07:18:04 [warn] 1965#1965: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 07:18:34 [warn] 1972#1972: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 07:19:04 [warn] 1979#1979: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 07:19:34 [warn] 1986#1986: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 07:20:04 [warn] 1993#1993: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 07:20:34 [warn] 2000#2000: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 07:21:05 [warn] 2007#2007: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 07:21:35 [warn] 2013#2013: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 07:22:05 [warn] 2020#2020: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 07:22:35 [warn] 2027#2027: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 07:23:05 [warn] 2034#2034: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 07:23:35 [warn] 2041#2041: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 07:24:05 [warn] 2048#2048: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 07:24:35 [warn] 2055#2055: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 07:25:05 [warn] 2062#2062: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 07:25:35 [warn] 2068#2068: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 07:26:05 [warn] 2075#2075: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 07:26:35 [warn] 2082#2082: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 07:27:05 [warn] 2088#2088: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 07:27:35 [warn] 2094#2094: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 07:28:05 [warn] 2101#2101: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 07:28:36 [warn] 2107#2107: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 07:29:06 [warn] 2114#2114: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 07:29:36 [warn] 2120#2120: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 07:30:06 [warn] 2126#2126: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 07:30:36 [warn] 2133#2133: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 07:31:06 [warn] 2140#2140: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 07:31:36 [warn] 2147#2147: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 07:32:06 [warn] 2154#2154: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 07:32:36 [warn] 2161#2161: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 07:33:06 [warn] 2168#2168: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 07:33:36 [warn] 2175#2175: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 07:34:06 [warn] 2181#2181: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 07:34:36 [warn] 2188#2188: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 07:35:06 [warn] 2194#2194: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 07:35:37 [warn] 2200#2200: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 07:36:01 [warn] 2206#2206: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 07:36:01 [notice] 2206#2206: signal process started
2025/07/25 07:36:01 [notice] 1#1: signal 1 (SIGHUP) received from 2206, reconfiguring
2025/07/25 07:36:01 [notice] 1#1: reconfiguring
2025/07/25 07:36:01 [warn] 1#1: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 07:36:01 [notice] 1#1: using the "epoll" event method
2025/07/25 07:36:01 [notice] 1#1: start worker processes
2025/07/25 07:36:01 [notice] 1#1: start worker process 2213
2025/07/25 07:36:01 [notice] 1#1: start worker process 2214
2025/07/25 07:36:01 [notice] 23#23: gracefully shutting down
2025/07/25 07:36:01 [notice] 22#22: gracefully shutting down
2025/07/25 07:36:01 [notice] 23#23: exiting
2025/07/25 07:36:01 [notice] 22#22: exiting
2025/07/25 07:36:01 [notice] 23#23: exit
2025/07/25 07:36:01 [notice] 22#22: exit
2025/07/25 07:36:01 [notice] 1#1: signal 17 (SIGCHLD) received from 22
2025/07/25 07:36:01 [notice] 1#1: worker process 22 exited with code 0
2025/07/25 07:36:01 [notice] 1#1: signal 29 (SIGIO) received
2025/07/25 07:36:01 [notice] 1#1: signal 17 (SIGCHLD) received from 23
2025/07/25 07:36:01 [notice] 1#1: worker process 23 exited with code 0
2025/07/25 07:36:01 [notice] 1#1: signal 29 (SIGIO) received
2025/07/25 07:36:07 [warn] 2215#2215: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 07:36:37 [warn] 2221#2221: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:14
2025/07/25 07:36:52 [notice] 2228#2228: signal process started
2025/07/25 07:36:52 [notice] 1#1: signal 1 (SIGHUP) received from 2228, reconfiguring
2025/07/25 07:36:52 [notice] 1#1: reconfiguring
2025/07/25 07:36:52 [notice] 1#1: using the "epoll" event method
2025/07/25 07:36:52 [notice] 1#1: start worker processes
2025/07/25 07:36:52 [notice] 1#1: start worker process 2235
2025/07/25 07:36:52 [notice] 1#1: start worker process 2236
2025/07/25 07:36:52 [notice] 2213#2213: gracefully shutting down
2025/07/25 07:36:52 [notice] 2213#2213: exiting
2025/07/25 07:36:52 [notice] 2214#2214: gracefully shutting down
2025/07/25 07:36:52 [notice] 2214#2214: exiting
2025/07/25 07:36:52 [notice] 2213#2213: exit
2025/07/25 07:36:52 [notice] 2214#2214: exit
2025/07/25 07:36:52 [notice] 1#1: signal 17 (SIGCHLD) received from 2214
2025/07/25 07:36:52 [notice] 1#1: worker process 2213 exited with code 0
2025/07/25 07:36:52 [notice] 1#1: worker process 2214 exited with code 0
2025/07/25 07:36:52 [notice] 1#1: signal 29 (SIGIO) received
2025/07/25 07:38:39 [notice] 2270#2270: signal process started
2025/07/25 07:38:39 [notice] 1#1: signal 1 (SIGHUP) received from 2270, reconfiguring
2025/07/25 07:38:39 [notice] 1#1: reconfiguring
2025/07/25 07:38:39 [notice] 1#1: using the "epoll" event method
2025/07/25 07:38:39 [notice] 1#1: start worker processes
2025/07/25 07:38:39 [notice] 1#1: start worker process 2275
2025/07/25 07:38:39 [notice] 1#1: start worker process 2276
2025/07/25 07:38:39 [notice] 2236#2236: gracefully shutting down
2025/07/25 07:38:39 [notice] 2235#2235: gracefully shutting down
2025/07/25 07:38:39 [notice] 2236#2236: exiting
2025/07/25 07:38:39 [notice] 2235#2235: exiting
2025/07/25 07:38:39 [notice] 2236#2236: exit
2025/07/25 07:38:39 [notice] 2235#2235: exit
2025/07/25 07:38:39 [notice] 1#1: signal 17 (SIGCHLD) received from 2236
2025/07/25 07:38:39 [notice] 1#1: worker process 2235 exited with code 0
2025/07/25 07:38:39 [notice] 1#1: worker process 2236 exited with code 0
2025/07/25 07:38:39 [notice] 1#1: signal 29 (SIGIO) received
2025/07/25 07:39:24 [notice] 2284#2284: signal process started
2025/07/25 07:39:24 [notice] 1#1: signal 1 (SIGHUP) received from 2284, reconfiguring
2025/07/25 07:39:24 [notice] 1#1: reconfiguring
2025/07/25 07:39:24 [notice] 1#1: using the "epoll" event method
2025/07/25 07:39:24 [notice] 1#1: start worker processes
2025/07/25 07:39:24 [notice] 1#1: start worker process 2291
2025/07/25 07:39:24 [notice] 1#1: start worker process 2292
2025/07/25 07:39:24 [notice] 2276#2276: gracefully shutting down
2025/07/25 07:39:24 [notice] 2275#2275: gracefully shutting down
2025/07/25 07:39:24 [notice] 2275#2275: exiting
2025/07/25 07:39:24 [notice] 2276#2276: exiting
2025/07/25 07:39:24 [notice] 2275#2275: exit
2025/07/25 07:39:24 [notice] 2276#2276: exit
2025/07/25 07:39:24 [notice] 1#1: signal 17 (SIGCHLD) received from 2275
2025/07/25 07:39:24 [notice] 1#1: worker process 2275 exited with code 0
2025/07/25 07:39:24 [notice] 1#1: worker process 2276 exited with code 0
2025/07/25 07:39:24 [notice] 1#1: signal 29 (SIGIO) received
2025/07/25 07:40:04 [notice] 1#1: signal 3 (SIGQUIT) received, shutting down
2025/07/25 07:40:04 [notice] 2291#2291: gracefully shutting down
2025/07/25 07:40:04 [notice] 2291#2291: exiting
2025/07/25 07:40:04 [notice] 2292#2292: gracefully shutting down
2025/07/25 07:40:04 [notice] 2292#2292: exiting
2025/07/25 07:40:04 [notice] 2291#2291: exit
2025/07/25 07:40:04 [notice] 2292#2292: exit
2025/07/25 07:40:04 [notice] 1#1: signal 17 (SIGCHLD) received from 2291
2025/07/25 07:40:04 [notice] 1#1: worker process 2291 exited with code 0
2025/07/25 07:40:04 [notice] 1#1: signal 29 (SIGIO) received
2025/07/25 07:40:04 [notice] 1#1: signal 17 (SIGCHLD) received from 2292
2025/07/25 07:40:04 [notice] 1#1: worker process 2292 exited with code 0
2025/07/25 07:40:04 [notice] 1#1: exit
2025/07/25 07:40:04 [notice] 1#1: using the "epoll" event method
2025/07/25 07:40:04 [notice] 1#1: nginx/1.29.0
2025/07/25 07:40:04 [notice] 1#1: built by gcc 14.2.0 (Alpine 14.2.0) 
2025/07/25 07:40:04 [notice] 1#1: OS: Linux 6.1.0-37-cloud-amd64
2025/07/25 07:40:04 [notice] 1#1: getrlimit(RLIMIT_NOFILE): 1048576:1048576
2025/07/25 07:40:04 [notice] 1#1: start worker processes
2025/07/25 07:40:04 [notice] 1#1: start worker process 21
2025/07/25 07:40:04 [notice] 1#1: start worker process 22
2025/07/25 07:51:13 [warn] 21#21: *20 an upstream response is buffered to a temporary file /var/cache/nginx/proxy_temp/1/00/0000000001 while reading upstream, client: ***************, server: liangliangdamowang.edu.deal, request: "GET /assets/semi-ui-BN13Wcpw.js HTTP/2.0", upstream: "http://**********:3000/assets/semi-ui-BN13Wcpw.js", host: "liangliangdamowang.edu.deal"
2025/07/25 08:28:35 [warn] 22#22: *56 an upstream response is buffered to a temporary file /var/cache/nginx/proxy_temp/2/00/0000000002 while reading upstream, client: ***************, server: liangliangdamowang.edu.deal, request: "GET /assets/react-core-DtMAePju.js HTTP/2.0", upstream: "http://**********:3000/assets/react-core-DtMAePju.js", host: "liangliangdamowang.edu.deal", referrer: "https://liangliangdamowang.edu.deal/new-api/"
2025/07/25 08:28:35 [warn] 22#22: *56 an upstream response is buffered to a temporary file /var/cache/nginx/proxy_temp/3/00/0000000003 while reading upstream, client: ***************, server: liangliangdamowang.edu.deal, request: "GET /assets/index-BbPB7pPU.js HTTP/2.0", upstream: "http://**********:3000/assets/index-BbPB7pPU.js", host: "liangliangdamowang.edu.deal", referrer: "https://liangliangdamowang.edu.deal/new-api/"
2025/07/25 08:28:35 [warn] 22#22: *56 an upstream response is buffered to a temporary file /var/cache/nginx/proxy_temp/4/00/0000000004 while reading upstream, client: ***************, server: liangliangdamowang.edu.deal, request: "GET /assets/semi-ui-yRXI6evF.css HTTP/2.0", upstream: "http://**********:3000/assets/semi-ui-yRXI6evF.css", host: "liangliangdamowang.edu.deal", referrer: "https://liangliangdamowang.edu.deal/new-api/"
2025/07/25 08:28:35 [warn] 22#22: *56 an upstream response is buffered to a temporary file /var/cache/nginx/proxy_temp/5/00/0000000005 while reading upstream, client: ***************, server: liangliangdamowang.edu.deal, request: "GET /assets/semi-ui-BN13Wcpw.js HTTP/2.0", upstream: "http://**********:3000/assets/semi-ui-BN13Wcpw.js", host: "liangliangdamowang.edu.deal", referrer: "https://liangliangdamowang.edu.deal/new-api/"
2025/07/25 08:28:35 [warn] 22#22: *56 an upstream response is buffered to a temporary file /var/cache/nginx/proxy_temp/6/00/0000000006 while reading upstream, client: ***************, server: liangliangdamowang.edu.deal, request: "GET /assets/index-C8L2jEvp.css HTTP/2.0", upstream: "http://**********:3000/assets/index-C8L2jEvp.css", host: "liangliangdamowang.edu.deal", referrer: "https://liangliangdamowang.edu.deal/new-api/"
2025/07/25 08:31:47 [warn] 690#690: duplicate MIME type "text/html" in /etc/nginx/conf.d/default.conf:75
2025/07/25 08:31:47 [warn] 696#696: duplicate MIME type "text/html" in /etc/nginx/conf.d/default.conf:75
2025/07/25 08:31:47 [notice] 696#696: signal process started
2025/07/25 08:31:47 [notice] 1#1: signal 1 (SIGHUP) received from 696, reconfiguring
2025/07/25 08:31:47 [notice] 1#1: reconfiguring
2025/07/25 08:31:47 [warn] 1#1: duplicate MIME type "text/html" in /etc/nginx/conf.d/default.conf:75
2025/07/25 08:31:47 [notice] 1#1: using the "epoll" event method
2025/07/25 08:31:47 [notice] 1#1: start worker processes
2025/07/25 08:31:47 [notice] 1#1: start worker process 703
2025/07/25 08:31:47 [notice] 1#1: start worker process 704
2025/07/25 08:31:47 [notice] 22#22: gracefully shutting down
2025/07/25 08:31:47 [notice] 22#22: exiting
2025/07/25 08:31:47 [notice] 21#21: gracefully shutting down
2025/07/25 08:31:47 [notice] 21#21: exiting
2025/07/25 08:31:47 [notice] 21#21: exit
2025/07/25 08:31:47 [notice] 22#22: exit
2025/07/25 08:31:47 [notice] 1#1: signal 17 (SIGCHLD) received from 22
2025/07/25 08:31:47 [notice] 1#1: worker process 22 exited with code 0
2025/07/25 08:31:47 [notice] 1#1: signal 29 (SIGIO) received
2025/07/25 08:31:47 [notice] 1#1: signal 17 (SIGCHLD) received from 21
2025/07/25 08:31:47 [notice] 1#1: worker process 21 exited with code 0
2025/07/25 08:31:47 [notice] 1#1: signal 29 (SIGIO) received
2025/07/25 08:32:10 [notice] 705#705: signal process started
2025/07/25 08:32:10 [notice] 1#1: signal 1 (SIGHUP) received from 705, reconfiguring
2025/07/25 08:32:10 [notice] 1#1: reconfiguring
2025/07/25 08:32:10 [notice] 1#1: using the "epoll" event method
2025/07/25 08:32:10 [notice] 1#1: start worker processes
2025/07/25 08:32:10 [notice] 1#1: start worker process 711
2025/07/25 08:32:10 [notice] 1#1: start worker process 712
2025/07/25 08:32:10 [notice] 703#703: gracefully shutting down
2025/07/25 08:32:10 [notice] 703#703: exiting
2025/07/25 08:32:10 [notice] 704#704: gracefully shutting down
2025/07/25 08:32:10 [notice] 704#704: exiting
2025/07/25 08:32:10 [notice] 704#704: exit
2025/07/25 08:32:10 [notice] 703#703: exit
2025/07/25 08:32:10 [notice] 1#1: signal 17 (SIGCHLD) received from 703
2025/07/25 08:32:10 [notice] 1#1: worker process 703 exited with code 0
2025/07/25 08:32:10 [notice] 1#1: signal 29 (SIGIO) received
2025/07/25 08:32:10 [notice] 1#1: signal 17 (SIGCHLD) received from 704
2025/07/25 08:32:10 [notice] 1#1: worker process 704 exited with code 0
2025/07/25 08:32:10 [notice] 1#1: signal 29 (SIGIO) received
2025/07/25 08:32:47 [warn] 720#720: duplicate MIME type "text/html" in /etc/nginx/conf.d/default.conf:72
2025/07/25 08:32:58 [warn] 726#726: duplicate MIME type "text/html" in /etc/nginx/conf.d/default.conf:72
2025/07/25 08:32:58 [notice] 726#726: signal process started
2025/07/25 08:32:58 [notice] 1#1: signal 1 (SIGHUP) received from 726, reconfiguring
2025/07/25 08:32:58 [notice] 1#1: reconfiguring
2025/07/25 08:32:58 [warn] 1#1: duplicate MIME type "text/html" in /etc/nginx/conf.d/default.conf:72
2025/07/25 08:32:58 [notice] 1#1: using the "epoll" event method
2025/07/25 08:32:58 [notice] 1#1: start worker processes
2025/07/25 08:32:58 [notice] 1#1: start worker process 732
2025/07/25 08:32:58 [notice] 1#1: start worker process 733
2025/07/25 08:32:58 [notice] 712#712: gracefully shutting down
2025/07/25 08:32:58 [notice] 712#712: exiting
2025/07/25 08:32:58 [notice] 711#711: gracefully shutting down
2025/07/25 08:32:58 [notice] 711#711: exiting
2025/07/25 08:32:58 [notice] 711#711: exit
2025/07/25 08:32:58 [notice] 712#712: exit
2025/07/25 08:32:58 [notice] 1#1: signal 17 (SIGCHLD) received from 711
2025/07/25 08:32:58 [notice] 1#1: worker process 711 exited with code 0
2025/07/25 08:32:58 [notice] 1#1: worker process 712 exited with code 0
2025/07/25 08:32:58 [notice] 1#1: signal 29 (SIGIO) received
2025/07/25 08:33:19 [notice] 740#740: signal process started
2025/07/25 08:33:19 [notice] 1#1: signal 1 (SIGHUP) received from 740, reconfiguring
2025/07/25 08:33:19 [notice] 1#1: reconfiguring
2025/07/25 08:33:19 [notice] 1#1: using the "epoll" event method
2025/07/25 08:33:19 [notice] 1#1: start worker processes
2025/07/25 08:33:19 [notice] 1#1: start worker process 746
2025/07/25 08:33:19 [notice] 1#1: start worker process 747
2025/07/25 08:33:20 [notice] 732#732: gracefully shutting down
2025/07/25 08:33:20 [notice] 733#733: gracefully shutting down
2025/07/25 08:33:20 [notice] 732#732: exiting
2025/07/25 08:33:20 [notice] 733#733: exiting
2025/07/25 08:33:20 [notice] 733#733: exit
2025/07/25 08:33:20 [notice] 732#732: exit
2025/07/25 08:33:20 [notice] 1#1: signal 17 (SIGCHLD) received from 733
2025/07/25 08:33:20 [notice] 1#1: worker process 732 exited with code 0
2025/07/25 08:33:20 [notice] 1#1: worker process 733 exited with code 0
2025/07/25 08:33:20 [notice] 1#1: signal 29 (SIGIO) received
2025/07/25 08:35:19 [notice] 776#776: signal process started
2025/07/25 08:35:19 [notice] 1#1: signal 1 (SIGHUP) received from 776, reconfiguring
2025/07/25 08:35:19 [notice] 1#1: reconfiguring
2025/07/25 08:35:19 [notice] 1#1: using the "epoll" event method
2025/07/25 08:35:19 [notice] 1#1: start worker processes
2025/07/25 08:35:19 [notice] 1#1: start worker process 783
2025/07/25 08:35:19 [notice] 1#1: start worker process 784
2025/07/25 08:35:19 [notice] 747#747: gracefully shutting down
2025/07/25 08:35:19 [notice] 747#747: exiting
2025/07/25 08:35:19 [notice] 746#746: gracefully shutting down
2025/07/25 08:35:19 [notice] 746#746: exiting
2025/07/25 08:35:19 [notice] 747#747: exit
2025/07/25 08:35:19 [notice] 746#746: exit
2025/07/25 08:35:19 [notice] 1#1: signal 17 (SIGCHLD) received from 747
2025/07/25 08:35:19 [notice] 1#1: worker process 746 exited with code 0
2025/07/25 08:35:19 [notice] 1#1: worker process 747 exited with code 0
2025/07/25 08:35:19 [notice] 1#1: signal 29 (SIGIO) received
2025/07/25 08:36:47 [warn] 797#797: duplicate MIME type "text/html" in /etc/nginx/conf.d/default.conf:72
2025/07/25 08:36:59 [warn] 802#802: duplicate MIME type "text/html" in /etc/nginx/conf.d/default.conf:72
2025/07/25 08:36:59 [notice] 802#802: signal process started
2025/07/25 08:36:59 [notice] 1#1: signal 1 (SIGHUP) received from 802, reconfiguring
2025/07/25 08:36:59 [notice] 1#1: reconfiguring
2025/07/25 08:36:59 [warn] 1#1: duplicate MIME type "text/html" in /etc/nginx/conf.d/default.conf:72
2025/07/25 08:36:59 [notice] 1#1: using the "epoll" event method
2025/07/25 08:36:59 [notice] 1#1: start worker processes
2025/07/25 08:36:59 [notice] 1#1: start worker process 809
2025/07/25 08:36:59 [notice] 1#1: start worker process 810
2025/07/25 08:37:00 [notice] 784#784: gracefully shutting down
2025/07/25 08:37:00 [notice] 784#784: exiting
2025/07/25 08:37:00 [notice] 783#783: gracefully shutting down
2025/07/25 08:37:00 [notice] 783#783: exiting
2025/07/25 08:37:00 [notice] 784#784: exit
2025/07/25 08:37:00 [notice] 783#783: exit
2025/07/25 08:37:00 [notice] 1#1: signal 17 (SIGCHLD) received from 784
2025/07/25 08:37:00 [notice] 1#1: worker process 783 exited with code 0
2025/07/25 08:37:00 [notice] 1#1: worker process 784 exited with code 0
2025/07/25 08:37:00 [notice] 1#1: signal 29 (SIGIO) received
2025/07/25 08:37:23 [notice] 818#818: signal process started
2025/07/25 08:37:23 [notice] 1#1: signal 1 (SIGHUP) received from 818, reconfiguring
2025/07/25 08:37:23 [notice] 1#1: reconfiguring
2025/07/25 08:37:23 [notice] 1#1: using the "epoll" event method
2025/07/25 08:37:23 [notice] 1#1: start worker processes
2025/07/25 08:37:23 [notice] 1#1: start worker process 825
2025/07/25 08:37:23 [notice] 1#1: start worker process 826
2025/07/25 08:37:24 [notice] 809#809: gracefully shutting down
2025/07/25 08:37:24 [notice] 809#809: exiting
2025/07/25 08:37:24 [notice] 810#810: gracefully shutting down
2025/07/25 08:37:24 [notice] 810#810: exiting
2025/07/25 08:37:24 [notice] 809#809: exit
2025/07/25 08:37:24 [notice] 810#810: exit
2025/07/25 08:37:24 [notice] 1#1: signal 17 (SIGCHLD) received from 810
2025/07/25 08:37:24 [notice] 1#1: worker process 809 exited with code 0
2025/07/25 08:37:24 [notice] 1#1: worker process 810 exited with code 0
2025/07/25 08:37:24 [notice] 1#1: signal 29 (SIGIO) received
2025/07/25 09:04:00 [warn] 825#825: *97 an upstream response is buffered to a temporary file /var/cache/nginx/proxy_temp/7/00/0000000007 while reading upstream, client: *************, server: liangliangdamowang.edu.deal, request: "GET /assets/index-BbPB7pPU.js HTTP/2.0", upstream: "http://**********:3000/assets/index-BbPB7pPU.js", host: "liangliangdamowang.edu.deal", referrer: "https://liangliangdamowang.edu.deal/new-api/"
2025/07/25 09:04:00 [warn] 825#825: *97 an upstream response is buffered to a temporary file /var/cache/nginx/proxy_temp/8/00/0000000008 while reading upstream, client: *************, server: liangliangdamowang.edu.deal, request: "GET /assets/react-core-DtMAePju.js HTTP/2.0", upstream: "http://**********:3000/assets/react-core-DtMAePju.js", host: "liangliangdamowang.edu.deal", referrer: "https://liangliangdamowang.edu.deal/new-api/"
2025/07/25 09:04:00 [warn] 825#825: *97 an upstream response is buffered to a temporary file /var/cache/nginx/proxy_temp/9/00/0000000009 while reading upstream, client: *************, server: liangliangdamowang.edu.deal, request: "GET /assets/semi-ui-BN13Wcpw.js HTTP/2.0", upstream: "http://**********:3000/assets/semi-ui-BN13Wcpw.js", host: "liangliangdamowang.edu.deal", referrer: "https://liangliangdamowang.edu.deal/new-api/"
2025/07/25 09:37:57 [warn] 826#826: *128 an upstream response is buffered to a temporary file /var/cache/nginx/proxy_temp/0/01/0000000010 while reading upstream, client: **************, server: liangliangdamowang.edu.deal, request: "GET /assets/react-core-DtMAePju.js HTTP/2.0", upstream: "http://**********:3000/assets/react-core-DtMAePju.js", host: "liangliangdamowang.edu.deal", referrer: "https://liangliangdamowang.edu.deal/new-api/"
2025/07/25 09:37:57 [warn] 826#826: *128 an upstream response is buffered to a temporary file /var/cache/nginx/proxy_temp/1/01/0000000011 while reading upstream, client: **************, server: liangliangdamowang.edu.deal, request: "GET /assets/semi-ui-BN13Wcpw.js HTTP/2.0", upstream: "http://**********:3000/assets/semi-ui-BN13Wcpw.js", host: "liangliangdamowang.edu.deal", referrer: "https://liangliangdamowang.edu.deal/new-api/"
2025/07/25 09:37:57 [warn] 826#826: *128 an upstream response is buffered to a temporary file /var/cache/nginx/proxy_temp/2/01/0000000012 while reading upstream, client: **************, server: liangliangdamowang.edu.deal, request: "GET /assets/index-BbPB7pPU.js HTTP/2.0", upstream: "http://**********:3000/assets/index-BbPB7pPU.js", host: "liangliangdamowang.edu.deal", referrer: "https://liangliangdamowang.edu.deal/new-api/"
2025/07/25 09:38:40 [warn] 1621#1621: duplicate MIME type "text/html" in /etc/nginx/conf.d/default.conf:72
2025/07/25 09:38:40 [notice] 1621#1621: signal process started
2025/07/25 09:38:40 [notice] 1#1: signal 1 (SIGHUP) received from 1621, reconfiguring
2025/07/25 09:38:40 [notice] 1#1: reconfiguring
2025/07/25 09:38:40 [warn] 1#1: duplicate MIME type "text/html" in /etc/nginx/conf.d/default.conf:72
2025/07/25 09:38:40 [notice] 1#1: using the "epoll" event method
2025/07/25 09:38:40 [notice] 1#1: start worker processes
2025/07/25 09:38:40 [notice] 1#1: start worker process 1628
2025/07/25 09:38:40 [notice] 1#1: start worker process 1629
2025/07/25 09:38:40 [notice] 825#825: gracefully shutting down
2025/07/25 09:38:40 [notice] 826#826: gracefully shutting down
2025/07/25 09:38:41 [notice] 825#825: exiting
2025/07/25 09:38:41 [notice] 825#825: exit
2025/07/25 09:38:41 [notice] 826#826: exiting
2025/07/25 09:38:41 [notice] 826#826: exit
2025/07/25 09:38:41 [notice] 1#1: signal 17 (SIGCHLD) received from 825
2025/07/25 09:38:41 [notice] 1#1: worker process 825 exited with code 0
2025/07/25 09:38:41 [notice] 1#1: signal 29 (SIGIO) received
2025/07/25 09:38:41 [notice] 1#1: signal 17 (SIGCHLD) received from 826
2025/07/25 09:38:41 [notice] 1#1: worker process 826 exited with code 0
2025/07/25 09:38:41 [notice] 1#1: signal 29 (SIGIO) received
2025/07/25 09:39:49 [notice] 1643#1643: signal process started
2025/07/25 09:39:49 [notice] 1#1: signal 1 (SIGHUP) received from 1643, reconfiguring
2025/07/25 09:39:49 [notice] 1#1: reconfiguring
2025/07/25 09:39:49 [notice] 1#1: using the "epoll" event method
2025/07/25 09:39:49 [notice] 1#1: start worker processes
2025/07/25 09:39:49 [notice] 1#1: start worker process 1650
2025/07/25 09:39:49 [notice] 1#1: start worker process 1651
2025/07/25 09:39:49 [notice] 1628#1628: gracefully shutting down
2025/07/25 09:39:49 [notice] 1628#1628: exiting
2025/07/25 09:39:49 [notice] 1629#1629: gracefully shutting down
2025/07/25 09:39:49 [notice] 1629#1629: exiting
2025/07/25 09:39:49 [notice] 1628#1628: exit
2025/07/25 09:39:49 [notice] 1629#1629: exit
2025/07/25 09:39:49 [notice] 1#1: signal 17 (SIGCHLD) received from 1628
2025/07/25 09:39:49 [notice] 1#1: worker process 1628 exited with code 0
2025/07/25 09:39:49 [notice] 1#1: signal 29 (SIGIO) received
2025/07/25 09:39:49 [notice] 1#1: signal 17 (SIGCHLD) received from 1629
2025/07/25 09:39:49 [notice] 1#1: worker process 1629 exited with code 0
2025/07/25 09:39:49 [notice] 1#1: signal 29 (SIGIO) received
2025/07/25 09:40:27 [error] 1650#1650: *152 directory index of "/var/www/love/" is forbidden, client: ************, server: liangliangdamowang.edu.deal, request: "HEAD /love/ HTTP/2.0", host: "liangliangdamowang.edu.deal"
2025/07/25 09:43:11 [warn] 1651#1651: *176 an upstream response is buffered to a temporary file /var/cache/nginx/proxy_temp/3/01/0000000013 while reading upstream, client: **********, server: liangliangdamowang.edu.deal, request: "GET /assets/index-BbPB7pPU.js HTTP/1.1", upstream: "http://**********:3000/assets/index-BbPB7pPU.js", host: "************"
2025/07/25 09:53:44 [warn] 1651#1651: *198 an upstream response is buffered to a temporary file /var/cache/nginx/proxy_temp/4/01/0000000014 while reading upstream, client: **********, server: liangliangdamowang.edu.deal, request: "GET /assets/semi-ui-yRXI6evF.css HTTP/2.0", upstream: "http://**********:3000/assets/semi-ui-yRXI6evF.css", host: "liangliangdamowang.edu.deal", referrer: "https://liangliangdamowang.edu.deal/new-api"
2025/07/25 09:53:44 [warn] 1651#1651: *198 an upstream response is buffered to a temporary file /var/cache/nginx/proxy_temp/5/01/0000000015 while reading upstream, client: **********, server: liangliangdamowang.edu.deal, request: "GET /assets/semi-ui-BN13Wcpw.js HTTP/2.0", upstream: "http://**********:3000/assets/semi-ui-BN13Wcpw.js", host: "liangliangdamowang.edu.deal", referrer: "https://liangliangdamowang.edu.deal/new-api"
2025/07/25 09:53:44 [warn] 1651#1651: *198 an upstream response is buffered to a temporary file /var/cache/nginx/proxy_temp/6/01/0000000016 while reading upstream, client: **********, server: liangliangdamowang.edu.deal, request: "GET /assets/index-C8L2jEvp.css HTTP/2.0", upstream: "http://**********:3000/assets/index-C8L2jEvp.css", host: "liangliangdamowang.edu.deal", referrer: "https://liangliangdamowang.edu.deal/new-api"
2025/07/25 09:53:44 [warn] 1651#1651: *198 an upstream response is buffered to a temporary file /var/cache/nginx/proxy_temp/7/01/0000000017 while reading upstream, client: **********, server: liangliangdamowang.edu.deal, request: "GET /assets/index-BbPB7pPU.js HTTP/2.0", upstream: "http://**********:3000/assets/index-BbPB7pPU.js", host: "liangliangdamowang.edu.deal", referrer: "https://liangliangdamowang.edu.deal/new-api"
2025/07/25 10:09:00 [emerg] 2031#2031: host not found in upstream "new-api" in /etc/nginx/conf.d/default.conf:50
2025/07/25 11:24:27 [notice] 3026#3026: signal process started
2025/07/25 11:24:27 [notice] 1#1: signal 1 (SIGHUP) received from 3026, reconfiguring
2025/07/25 11:24:27 [notice] 1#1: reconfiguring
2025/07/25 11:24:27 [notice] 1#1: using the "epoll" event method
2025/07/25 11:24:27 [notice] 1#1: start worker processes
2025/07/25 11:24:27 [notice] 1#1: start worker process 3032
2025/07/25 11:24:27 [notice] 1#1: start worker process 3033
2025/07/25 11:24:27 [notice] 1651#1651: gracefully shutting down
2025/07/25 11:24:27 [notice] 1651#1651: exiting
2025/07/25 11:24:27 [notice] 1650#1650: gracefully shutting down
2025/07/25 11:24:27 [notice] 1650#1650: exiting
2025/07/25 11:24:27 [notice] 1651#1651: exit
2025/07/25 11:24:27 [notice] 1650#1650: exit
2025/07/25 11:24:27 [notice] 1#1: signal 17 (SIGCHLD) received from 1651
2025/07/25 11:24:27 [notice] 1#1: worker process 1651 exited with code 0
2025/07/25 11:24:27 [notice] 1#1: signal 29 (SIGIO) received
2025/07/25 11:24:27 [notice] 1#1: signal 17 (SIGCHLD) received from 1650
2025/07/25 11:24:27 [notice] 1#1: worker process 1650 exited with code 0
2025/07/25 11:24:27 [notice] 1#1: signal 29 (SIGIO) received
2025/07/25 11:27:18 [notice] 3086#3086: signal process started
2025/07/25 11:27:18 [notice] 1#1: signal 1 (SIGHUP) received from 3086, reconfiguring
2025/07/25 11:27:18 [notice] 1#1: reconfiguring
2025/07/25 11:27:18 [notice] 1#1: using the "epoll" event method
2025/07/25 11:27:18 [notice] 1#1: start worker processes
2025/07/25 11:27:18 [notice] 1#1: start worker process 3093
2025/07/25 11:27:18 [notice] 1#1: start worker process 3094
2025/07/25 11:27:18 [notice] 3032#3032: gracefully shutting down
2025/07/25 11:27:18 [notice] 3032#3032: exiting
2025/07/25 11:27:18 [notice] 3033#3033: gracefully shutting down
2025/07/25 11:27:18 [notice] 3033#3033: exiting
2025/07/25 11:27:18 [notice] 3032#3032: exit
2025/07/25 11:27:18 [notice] 3033#3033: exit
2025/07/25 11:27:18 [notice] 1#1: signal 17 (SIGCHLD) received from 3032
2025/07/25 11:27:18 [notice] 1#1: worker process 3032 exited with code 0
2025/07/25 11:27:18 [notice] 1#1: signal 29 (SIGIO) received
2025/07/25 11:27:18 [notice] 1#1: signal 17 (SIGCHLD) received from 3033
2025/07/25 11:27:18 [notice] 1#1: worker process 3033 exited with code 0
2025/07/25 11:27:18 [notice] 1#1: signal 29 (SIGIO) received
2025/07/25 11:28:22 [notice] 3122#3122: signal process started
2025/07/25 11:28:22 [notice] 1#1: signal 1 (SIGHUP) received from 3122, reconfiguring
2025/07/25 11:28:22 [notice] 1#1: reconfiguring
2025/07/25 11:28:22 [notice] 1#1: using the "epoll" event method
2025/07/25 11:28:22 [notice] 1#1: start worker processes
2025/07/25 11:28:22 [notice] 1#1: start worker process 3128
2025/07/25 11:28:22 [notice] 1#1: start worker process 3129
2025/07/25 11:28:22 [notice] 3093#3093: gracefully shutting down
2025/07/25 11:28:22 [notice] 3093#3093: exiting
2025/07/25 11:28:22 [notice] 3093#3093: exit
2025/07/25 11:28:22 [notice] 3094#3094: gracefully shutting down
2025/07/25 11:28:22 [notice] 3094#3094: exiting
2025/07/25 11:28:22 [notice] 3094#3094: exit
2025/07/25 11:28:22 [notice] 1#1: signal 17 (SIGCHLD) received from 3094
2025/07/25 11:28:22 [notice] 1#1: worker process 3093 exited with code 0
2025/07/25 11:28:22 [notice] 1#1: worker process 3094 exited with code 0
2025/07/25 11:28:22 [notice] 1#1: signal 29 (SIGIO) received
2025/07/25 11:29:29 [notice] 3143#3143: signal process started
2025/07/25 11:29:29 [notice] 1#1: signal 1 (SIGHUP) received from 3143, reconfiguring
2025/07/25 11:29:29 [notice] 1#1: reconfiguring
2025/07/25 11:29:29 [notice] 1#1: using the "epoll" event method
2025/07/25 11:29:29 [notice] 1#1: start worker processes
2025/07/25 11:29:29 [notice] 1#1: start worker process 3150
2025/07/25 11:29:29 [notice] 1#1: start worker process 3151
2025/07/25 11:29:29 [notice] 3128#3128: gracefully shutting down
2025/07/25 11:29:29 [notice] 3129#3129: gracefully shutting down
2025/07/25 11:29:29 [notice] 3128#3128: exiting
2025/07/25 11:29:29 [notice] 3128#3128: exit
2025/07/25 11:29:29 [notice] 1#1: signal 17 (SIGCHLD) received from 3128
2025/07/25 11:29:29 [notice] 1#1: worker process 3128 exited with code 0
2025/07/25 11:29:29 [notice] 1#1: signal 29 (SIGIO) received
2025/07/25 11:29:30 [notice] 3129#3129: exiting
2025/07/25 11:29:30 [notice] 3129#3129: exit
2025/07/25 11:29:30 [notice] 1#1: signal 17 (SIGCHLD) received from 3129
2025/07/25 11:29:30 [notice] 1#1: worker process 3129 exited with code 0
2025/07/25 11:29:30 [notice] 1#1: signal 29 (SIGIO) received
2025/07/25 11:32:00 [notice] 3206#3206: signal process started
2025/07/25 11:32:00 [notice] 1#1: signal 1 (SIGHUP) received from 3206, reconfiguring
2025/07/25 11:32:00 [notice] 1#1: reconfiguring
2025/07/25 11:32:00 [notice] 1#1: using the "epoll" event method
2025/07/25 11:32:00 [notice] 1#1: start worker processes
2025/07/25 11:32:00 [notice] 1#1: start worker process 3213
2025/07/25 11:32:00 [notice] 1#1: start worker process 3214
2025/07/25 11:32:00 [notice] 3150#3150: gracefully shutting down
2025/07/25 11:32:00 [notice] 3150#3150: exiting
2025/07/25 11:32:00 [notice] 3151#3151: gracefully shutting down
2025/07/25 11:32:00 [notice] 3151#3151: exiting
2025/07/25 11:32:00 [notice] 3151#3151: exit
2025/07/25 11:32:00 [notice] 3150#3150: exit
2025/07/25 11:32:00 [notice] 1#1: signal 17 (SIGCHLD) received from 3150
2025/07/25 11:32:00 [notice] 1#1: worker process 3150 exited with code 0
2025/07/25 11:32:00 [notice] 1#1: signal 29 (SIGIO) received
2025/07/25 11:32:00 [notice] 1#1: signal 17 (SIGCHLD) received from 3151
2025/07/25 11:32:00 [notice] 1#1: worker process 3151 exited with code 0
2025/07/25 11:32:00 [notice] 1#1: signal 29 (SIGIO) received
2025/07/25 11:34:34 [warn] 3214#3214: *341 an upstream response is buffered to a temporary file /var/cache/nginx/proxy_temp/8/01/0000000018 while reading upstream, client: **********, server: liangliangdamowang.edu.deal, request: "GET /assets/index-BbPB7pPU.js HTTP/2.0", upstream: "http://**********:3000/assets/index-BbPB7pPU.js", host: "liangliangdamowang.edu.deal", referrer: "https://liangliangdamowang.edu.deal/ai/"
2025/07/25 11:34:34 [warn] 3214#3214: *341 an upstream response is buffered to a temporary file /var/cache/nginx/proxy_temp/9/01/0000000019 while reading upstream, client: **********, server: liangliangdamowang.edu.deal, request: "GET /assets/semi-ui-BN13Wcpw.js HTTP/2.0", upstream: "http://**********:3000/assets/semi-ui-BN13Wcpw.js", host: "liangliangdamowang.edu.deal", referrer: "https://liangliangdamowang.edu.deal/ai/"
2025/07/25 11:34:34 [warn] 3214#3214: *341 an upstream response is buffered to a temporary file /var/cache/nginx/proxy_temp/0/02/0000000020 while reading upstream, client: **********, server: liangliangdamowang.edu.deal, request: "GET /assets/semi-ui-yRXI6evF.css HTTP/2.0", upstream: "http://**********:3000/assets/semi-ui-yRXI6evF.css", host: "liangliangdamowang.edu.deal", referrer: "https://liangliangdamowang.edu.deal/ai/"
2025/07/25 11:34:34 [warn] 3214#3214: *341 an upstream response is buffered to a temporary file /var/cache/nginx/proxy_temp/1/02/0000000021 while reading upstream, client: **********, server: liangliangdamowang.edu.deal, request: "GET /assets/index-C8L2jEvp.css HTTP/2.0", upstream: "http://**********:3000/assets/index-C8L2jEvp.css", host: "liangliangdamowang.edu.deal", referrer: "https://liangliangdamowang.edu.deal/ai/"
2025/07/25 11:37:27 [notice] 3292#3292: signal process started
2025/07/25 11:37:27 [notice] 1#1: signal 1 (SIGHUP) received from 3292, reconfiguring
2025/07/25 11:37:27 [notice] 1#1: reconfiguring
2025/07/25 11:37:27 [notice] 1#1: using the "epoll" event method
2025/07/25 11:37:27 [notice] 1#1: start worker processes
2025/07/25 11:37:27 [notice] 1#1: start worker process 3299
2025/07/25 11:37:27 [notice] 1#1: start worker process 3300
2025/07/25 11:37:27 [notice] 3213#3213: gracefully shutting down
2025/07/25 11:37:27 [notice] 3213#3213: exiting
2025/07/25 11:37:27 [notice] 3214#3214: gracefully shutting down
2025/07/25 11:37:27 [notice] 3214#3214: exiting
2025/07/25 11:37:27 [notice] 3213#3213: exit
2025/07/25 11:37:27 [notice] 3214#3214: exit
2025/07/25 11:37:27 [notice] 1#1: signal 17 (SIGCHLD) received from 3213
2025/07/25 11:37:27 [notice] 1#1: worker process 3213 exited with code 0
2025/07/25 11:37:27 [notice] 1#1: signal 29 (SIGIO) received
2025/07/25 11:37:27 [notice] 1#1: signal 17 (SIGCHLD) received from 3214
2025/07/25 11:37:27 [notice] 1#1: worker process 3214 exited with code 0
2025/07/25 11:37:27 [notice] 1#1: signal 29 (SIGIO) received
2025/07/25 11:38:35 [notice] 3314#3314: signal process started
2025/07/25 11:38:35 [notice] 1#1: signal 1 (SIGHUP) received from 3314, reconfiguring
2025/07/25 11:38:35 [notice] 1#1: reconfiguring
2025/07/25 11:38:35 [notice] 1#1: using the "epoll" event method
2025/07/25 11:38:35 [notice] 1#1: start worker processes
2025/07/25 11:38:35 [notice] 1#1: start worker process 3321
2025/07/25 11:38:35 [notice] 1#1: start worker process 3322
2025/07/25 11:38:35 [notice] 3299#3299: gracefully shutting down
2025/07/25 11:38:35 [notice] 3300#3300: gracefully shutting down
2025/07/25 11:38:35 [notice] 3299#3299: exiting
2025/07/25 11:38:35 [notice] 3300#3300: exiting
2025/07/25 11:38:35 [notice] 3299#3299: exit
2025/07/25 11:38:35 [notice] 3300#3300: exit
2025/07/25 11:38:35 [notice] 1#1: signal 17 (SIGCHLD) received from 3299
2025/07/25 11:38:35 [notice] 1#1: worker process 3299 exited with code 0
2025/07/25 11:38:35 [notice] 1#1: signal 29 (SIGIO) received
2025/07/25 11:38:35 [notice] 1#1: signal 17 (SIGCHLD) received from 3300
2025/07/25 11:38:35 [notice] 1#1: worker process 3300 exited with code 0
2025/07/25 11:38:35 [notice] 1#1: signal 29 (SIGIO) received
2025/07/25 11:41:07 [notice] 3363#3363: signal process started
2025/07/25 11:41:07 [notice] 1#1: signal 1 (SIGHUP) received from 3363, reconfiguring
2025/07/25 11:41:07 [notice] 1#1: reconfiguring
2025/07/25 11:41:07 [notice] 1#1: using the "epoll" event method
2025/07/25 11:41:07 [notice] 1#1: start worker processes
2025/07/25 11:41:07 [notice] 1#1: start worker process 3370
2025/07/25 11:41:07 [notice] 1#1: start worker process 3371
2025/07/25 11:41:07 [notice] 3322#3322: gracefully shutting down
2025/07/25 11:41:07 [notice] 3322#3322: exiting
2025/07/25 11:41:07 [notice] 3321#3321: gracefully shutting down
2025/07/25 11:41:07 [notice] 3322#3322: exit
2025/07/25 11:41:07 [notice] 1#1: signal 17 (SIGCHLD) received from 3322
2025/07/25 11:41:07 [notice] 1#1: worker process 3322 exited with code 0
2025/07/25 11:41:07 [notice] 1#1: signal 29 (SIGIO) received
2025/07/25 11:41:07 [notice] 3321#3321: exiting
2025/07/25 11:41:07 [notice] 3321#3321: exit
2025/07/25 11:41:07 [notice] 1#1: signal 17 (SIGCHLD) received from 3321
2025/07/25 11:41:07 [notice] 1#1: worker process 3321 exited with code 0
2025/07/25 11:41:07 [notice] 1#1: signal 29 (SIGIO) received
2025/07/25 11:43:48 [notice] 3419#3419: signal process started
2025/07/25 11:43:48 [notice] 1#1: signal 1 (SIGHUP) received from 3419, reconfiguring
2025/07/25 11:43:48 [notice] 1#1: reconfiguring
2025/07/25 11:43:48 [notice] 1#1: using the "epoll" event method
2025/07/25 11:43:48 [notice] 1#1: start worker processes
2025/07/25 11:43:48 [notice] 1#1: start worker process 3426
2025/07/25 11:43:48 [notice] 1#1: start worker process 3427
2025/07/25 11:43:49 [notice] 3371#3371: gracefully shutting down
2025/07/25 11:43:49 [notice] 3371#3371: exiting
2025/07/25 11:43:49 [notice] 3370#3370: gracefully shutting down
2025/07/25 11:43:49 [notice] 3370#3370: exiting
2025/07/25 11:43:49 [notice] 3371#3371: exit
2025/07/25 11:43:49 [notice] 3370#3370: exit
2025/07/25 11:43:49 [notice] 1#1: signal 17 (SIGCHLD) received from 3370
2025/07/25 11:43:49 [notice] 1#1: worker process 3370 exited with code 0
2025/07/25 11:43:49 [notice] 1#1: worker process 3371 exited with code 0
2025/07/25 11:43:49 [notice] 1#1: signal 29 (SIGIO) received
2025/07/25 11:47:13 [notice] 3468#3468: signal process started
2025/07/25 11:47:14 [notice] 1#1: signal 1 (SIGHUP) received from 3468, reconfiguring
2025/07/25 11:47:14 [notice] 1#1: reconfiguring
2025/07/25 11:47:14 [notice] 1#1: using the "epoll" event method
2025/07/25 11:47:14 [notice] 1#1: start worker processes
2025/07/25 11:47:14 [notice] 1#1: start worker process 3474
2025/07/25 11:47:14 [notice] 1#1: start worker process 3475
2025/07/25 11:47:14 [notice] 3426#3426: gracefully shutting down
2025/07/25 11:47:14 [notice] 3426#3426: exiting
2025/07/25 11:47:14 [notice] 3427#3427: gracefully shutting down
2025/07/25 11:47:14 [notice] 3427#3427: exiting
2025/07/25 11:47:14 [notice] 3426#3426: exit
2025/07/25 11:47:14 [notice] 3427#3427: exit
2025/07/25 11:47:14 [notice] 1#1: signal 17 (SIGCHLD) received from 3427
2025/07/25 11:47:14 [notice] 1#1: worker process 3427 exited with code 0
2025/07/25 11:47:14 [notice] 1#1: signal 29 (SIGIO) received
2025/07/25 11:47:14 [notice] 1#1: signal 17 (SIGCHLD) received from 3426
2025/07/25 11:47:14 [notice] 1#1: worker process 3426 exited with code 0
2025/07/25 11:47:14 [notice] 1#1: signal 29 (SIGIO) received
2025/07/25 11:49:30 [warn] 3475#3475: *388 an upstream response is buffered to a temporary file /var/cache/nginx/proxy_temp/2/02/0000000022 while reading upstream, client: **********, server: liangliangdamowang.edu.deal, request: "GET /assets/index-BbPB7pPU.js HTTP/2.0", upstream: "http://**********:3000/assets/index-BbPB7pPU.js", host: "liangliangdamowang.edu.deal", referrer: "https://liangliangdamowang.edu.deal/ai/"
2025/07/25 11:49:30 [warn] 3475#3475: *388 an upstream response is buffered to a temporary file /var/cache/nginx/proxy_temp/3/02/0000000023 while reading upstream, client: **********, server: liangliangdamowang.edu.deal, request: "GET /assets/react-core-DtMAePju.js HTTP/2.0", upstream: "http://**********:3000/assets/react-core-DtMAePju.js", host: "liangliangdamowang.edu.deal", referrer: "https://liangliangdamowang.edu.deal/ai/"
2025/07/25 11:49:30 [warn] 3475#3475: *388 an upstream response is buffered to a temporary file /var/cache/nginx/proxy_temp/4/02/0000000024 while reading upstream, client: **********, server: liangliangdamowang.edu.deal, request: "GET /assets/semi-ui-BN13Wcpw.js HTTP/2.0", upstream: "http://**********:3000/assets/semi-ui-BN13Wcpw.js", host: "liangliangdamowang.edu.deal", referrer: "https://liangliangdamowang.edu.deal/ai/"
2025/07/25 11:49:30 [warn] 3475#3475: *388 an upstream response is buffered to a temporary file /var/cache/nginx/proxy_temp/5/02/0000000025 while reading upstream, client: **********, server: liangliangdamowang.edu.deal, request: "GET /assets/semi-ui-yRXI6evF.css HTTP/2.0", upstream: "http://**********:3000/assets/semi-ui-yRXI6evF.css", host: "liangliangdamowang.edu.deal", referrer: "https://liangliangdamowang.edu.deal/ai/"
2025/07/25 11:49:30 [warn] 3475#3475: *388 an upstream response is buffered to a temporary file /var/cache/nginx/proxy_temp/6/02/0000000026 while reading upstream, client: **********, server: liangliangdamowang.edu.deal, request: "GET /assets/index-C8L2jEvp.css HTTP/2.0", upstream: "http://**********:3000/assets/index-C8L2jEvp.css", host: "liangliangdamowang.edu.deal", referrer: "https://liangliangdamowang.edu.deal/ai/"
2025/07/25 11:53:07 [notice] 3562#3562: signal process started
2025/07/25 11:53:07 [notice] 1#1: signal 1 (SIGHUP) received from 3562, reconfiguring
2025/07/25 11:53:07 [notice] 1#1: reconfiguring
2025/07/25 11:53:07 [notice] 1#1: using the "epoll" event method
2025/07/25 11:53:07 [notice] 1#1: start worker processes
2025/07/25 11:53:07 [notice] 1#1: start worker process 3568
2025/07/25 11:53:07 [notice] 1#1: start worker process 3569
2025/07/25 11:53:07 [notice] 3474#3474: gracefully shutting down
2025/07/25 11:53:07 [notice] 3474#3474: exiting
2025/07/25 11:53:07 [notice] 3475#3475: gracefully shutting down
2025/07/25 11:53:07 [notice] 3475#3475: exiting
2025/07/25 11:53:07 [notice] 3474#3474: exit
2025/07/25 11:53:07 [notice] 3475#3475: exit
2025/07/25 11:53:07 [notice] 1#1: signal 17 (SIGCHLD) received from 3474
2025/07/25 11:53:07 [notice] 1#1: worker process 3474 exited with code 0
2025/07/25 11:53:07 [notice] 1#1: signal 29 (SIGIO) received
2025/07/25 11:53:07 [notice] 1#1: signal 17 (SIGCHLD) received from 3475
2025/07/25 11:53:07 [notice] 1#1: worker process 3475 exited with code 0
2025/07/25 11:53:07 [notice] 1#1: signal 29 (SIGIO) received
2025/07/25 11:56:15 [warn] 3569#3569: *487 an upstream response is buffered to a temporary file /var/cache/nginx/proxy_temp/7/02/0000000027 while reading upstream, client: **********, server: liangliangdamowang.edu.deal, request: "GET /assets/semi-ui-yRXI6evF.css HTTP/2.0", upstream: "http://**********:3000/assets/semi-ui-yRXI6evF.css", host: "liangliangdamowang.edu.deal", referrer: "https://liangliangdamowang.edu.deal/ai/console/channel"
2025/07/25 11:56:15 [warn] 3569#3569: *487 an upstream response is buffered to a temporary file /var/cache/nginx/proxy_temp/8/02/0000000028 while reading upstream, client: **********, server: liangliangdamowang.edu.deal, request: "GET /assets/semi-ui-BN13Wcpw.js HTTP/2.0", upstream: "http://**********:3000/assets/semi-ui-BN13Wcpw.js", host: "liangliangdamowang.edu.deal", referrer: "https://liangliangdamowang.edu.deal/ai/console/channel"
2025/07/25 11:56:15 [warn] 3569#3569: *487 an upstream response is buffered to a temporary file /var/cache/nginx/proxy_temp/9/02/0000000029 while reading upstream, client: **********, server: liangliangdamowang.edu.deal, request: "GET /assets/index-BbPB7pPU.js HTTP/2.0", upstream: "http://**********:3000/assets/index-BbPB7pPU.js", host: "liangliangdamowang.edu.deal", referrer: "https://liangliangdamowang.edu.deal/ai/console/channel"
2025/07/25 12:01:49 [emerg] 3675#3675: unknown directive "proxy_Set_header" in /etc/nginx/conf.d/default.conf:46
2025/07/25 12:02:19 [warn] 3681#3681: duplicate MIME type "text/html" in /etc/nginx/conf.d/default.conf:51
2025/07/25 12:02:20 [warn] 3687#3687: duplicate MIME type "text/html" in /etc/nginx/conf.d/default.conf:51
2025/07/25 12:02:20 [warn] 3693#3693: duplicate MIME type "text/html" in /etc/nginx/conf.d/default.conf:51
2025/07/25 12:02:20 [notice] 3693#3693: signal process started
2025/07/25 12:02:20 [notice] 1#1: signal 1 (SIGHUP) received from 3693, reconfiguring
2025/07/25 12:02:20 [notice] 1#1: reconfiguring
2025/07/25 12:02:20 [warn] 1#1: duplicate MIME type "text/html" in /etc/nginx/conf.d/default.conf:51
2025/07/25 12:02:20 [notice] 1#1: using the "epoll" event method
2025/07/25 12:02:20 [notice] 1#1: start worker processes
2025/07/25 12:02:20 [notice] 1#1: start worker process 3699
2025/07/25 12:02:20 [notice] 1#1: start worker process 3700
2025/07/25 12:02:20 [notice] 3569#3569: gracefully shutting down
2025/07/25 12:02:20 [notice] 3569#3569: exiting
2025/07/25 12:02:20 [notice] 3568#3568: gracefully shutting down
2025/07/25 12:02:20 [notice] 3568#3568: exiting
2025/07/25 12:02:20 [notice] 3568#3568: exit
2025/07/25 12:02:20 [notice] 3569#3569: exit
2025/07/25 12:02:20 [notice] 1#1: signal 17 (SIGCHLD) received from 3569
2025/07/25 12:02:20 [notice] 1#1: worker process 3568 exited with code 0
2025/07/25 12:02:20 [notice] 1#1: worker process 3569 exited with code 0
2025/07/25 12:02:20 [notice] 1#1: signal 29 (SIGIO) received
2025/07/25 12:02:52 [warn] 3701#3701: duplicate MIME type "text/html" in /etc/nginx/conf.d/default.conf:51
2025/07/25 12:03:22 [warn] 3708#3708: duplicate MIME type "text/html" in /etc/nginx/conf.d/default.conf:51
2025/07/25 12:03:52 [warn] 3715#3715: duplicate MIME type "text/html" in /etc/nginx/conf.d/default.conf:51
2025/07/25 12:04:22 [warn] 3721#3721: duplicate MIME type "text/html" in /etc/nginx/conf.d/default.conf:51
2025/07/25 12:04:52 [warn] 3728#3728: duplicate MIME type "text/html" in /etc/nginx/conf.d/default.conf:51
2025/07/25 12:05:22 [warn] 3735#3735: duplicate MIME type "text/html" in /etc/nginx/conf.d/default.conf:51
2025/07/25 12:06:53 [warn] 3754#3754: duplicate MIME type "text/html" in /etc/nginx/conf.d/default.conf:113
2025/07/25 12:07:05 [warn] 3761#3761: duplicate MIME type "text/html" in /etc/nginx/conf.d/default.conf:113
2025/07/25 12:07:05 [warn] 3767#3767: duplicate MIME type "text/html" in /etc/nginx/conf.d/default.conf:113
2025/07/25 12:07:05 [notice] 3767#3767: signal process started
2025/07/25 12:07:05 [notice] 1#1: signal 1 (SIGHUP) received from 3767, reconfiguring
2025/07/25 12:07:05 [notice] 1#1: reconfiguring
2025/07/25 12:07:05 [warn] 1#1: duplicate MIME type "text/html" in /etc/nginx/conf.d/default.conf:113
2025/07/25 12:07:05 [notice] 1#1: using the "epoll" event method
2025/07/25 12:07:05 [notice] 1#1: start worker processes
2025/07/25 12:07:05 [notice] 1#1: start worker process 3773
2025/07/25 12:07:05 [notice] 1#1: start worker process 3774
2025/07/25 12:07:06 [notice] 3700#3700: gracefully shutting down
2025/07/25 12:07:06 [notice] 3700#3700: exiting
2025/07/25 12:07:06 [notice] 3699#3699: gracefully shutting down
2025/07/25 12:07:06 [notice] 3699#3699: exiting
2025/07/25 12:07:06 [notice] 3700#3700: exit
2025/07/25 12:07:06 [notice] 3699#3699: exit
2025/07/25 12:07:06 [notice] 1#1: signal 17 (SIGCHLD) received from 3700
2025/07/25 12:07:06 [notice] 1#1: worker process 3699 exited with code 0
2025/07/25 12:07:06 [notice] 1#1: worker process 3700 exited with code 0
2025/07/25 12:07:06 [notice] 1#1: signal 29 (SIGIO) received
2025/07/25 12:07:23 [warn] 3775#3775: duplicate MIME type "text/html" in /etc/nginx/conf.d/default.conf:113
2025/07/25 12:07:53 [warn] 3781#3781: duplicate MIME type "text/html" in /etc/nginx/conf.d/default.conf:113
2025/07/25 12:08:23 [warn] 3788#3788: duplicate MIME type "text/html" in /etc/nginx/conf.d/default.conf:113
2025/07/25 12:08:53 [warn] 3794#3794: duplicate MIME type "text/html" in /etc/nginx/conf.d/default.conf:113
2025/07/25 12:09:23 [warn] 3800#3800: duplicate MIME type "text/html" in /etc/nginx/conf.d/default.conf:113
2025/07/25 12:09:53 [emerg] 3806#3806: unknown directive "proxy_Set_header" in /etc/nginx/conf.d/default.conf:96
2025/07/25 12:10:23 [warn] 3812#3812: duplicate MIME type "text/html" in /etc/nginx/conf.d/default.conf:146
2025/07/25 12:10:23 [emerg] 3812#3812: duplicate location "/ai" in /etc/nginx/conf.d/default.conf:150
2025/07/25 12:10:33 [warn] 3819#3819: duplicate MIME type "text/html" in /etc/nginx/conf.d/default.conf:146
2025/07/25 12:10:33 [emerg] 3819#3819: duplicate location "/ai" in /etc/nginx/conf.d/default.conf:150
2025/07/25 12:10:53 [warn] 3826#3826: duplicate MIME type "text/html" in /etc/nginx/conf.d/default.conf:146
2025/07/25 12:10:53 [emerg] 3826#3826: duplicate location "/ai" in /etc/nginx/conf.d/default.conf:150
2025/07/25 12:11:23 [warn] 3832#3832: duplicate MIME type "text/html" in /etc/nginx/conf.d/default.conf:146
2025/07/25 12:11:23 [emerg] 3832#3832: duplicate location "/ai" in /etc/nginx/conf.d/default.conf:150
2025/07/25 12:11:53 [warn] 3838#3838: duplicate MIME type "text/html" in /etc/nginx/conf.d/default.conf:146
2025/07/25 12:11:56 [warn] 3845#3845: duplicate MIME type "text/html" in /etc/nginx/conf.d/default.conf:146
2025/07/25 12:11:56 [warn] 3851#3851: duplicate MIME type "text/html" in /etc/nginx/conf.d/default.conf:146
2025/07/25 12:11:56 [notice] 3851#3851: signal process started
2025/07/25 12:11:56 [notice] 1#1: signal 1 (SIGHUP) received from 3851, reconfiguring
2025/07/25 12:11:56 [notice] 1#1: reconfiguring
2025/07/25 12:11:56 [warn] 1#1: duplicate MIME type "text/html" in /etc/nginx/conf.d/default.conf:146
2025/07/25 12:11:56 [notice] 1#1: using the "epoll" event method
2025/07/25 12:11:56 [notice] 1#1: start worker processes
2025/07/25 12:11:56 [notice] 1#1: start worker process 3858
2025/07/25 12:11:56 [notice] 1#1: start worker process 3859
2025/07/25 12:11:56 [notice] 3773#3773: gracefully shutting down
2025/07/25 12:11:56 [notice] 3774#3774: gracefully shutting down
2025/07/25 12:11:56 [notice] 3773#3773: exiting
2025/07/25 12:11:56 [notice] 3774#3774: exiting
2025/07/25 12:11:56 [notice] 3773#3773: exit
2025/07/25 12:11:56 [notice] 3774#3774: exit
2025/07/25 12:11:56 [notice] 1#1: signal 17 (SIGCHLD) received from 3774
2025/07/25 12:11:56 [notice] 1#1: worker process 3773 exited with code 0
2025/07/25 12:11:56 [notice] 1#1: worker process 3774 exited with code 0
2025/07/25 12:11:56 [notice] 1#1: signal 29 (SIGIO) received
2025/07/25 12:12:23 [warn] 3860#3860: duplicate MIME type "text/html" in /etc/nginx/conf.d/default.conf:146
2025/07/25 12:12:53 [warn] 3867#3867: duplicate MIME type "text/html" in /etc/nginx/conf.d/default.conf:146
2025/07/25 12:13:23 [warn] 3874#3874: duplicate MIME type "text/html" in /etc/nginx/conf.d/default.conf:146
2025/07/25 12:13:54 [warn] 3888#3888: duplicate MIME type "text/html" in /etc/nginx/conf.d/default.conf:146
2025/07/25 12:14:24 [warn] 3894#3894: duplicate MIME type "text/html" in /etc/nginx/conf.d/default.conf:146
2025/07/25 12:14:54 [warn] 3900#3900: duplicate MIME type "text/html" in /etc/nginx/conf.d/default.conf:146
2025/07/25 12:15:24 [warn] 3906#3906: duplicate MIME type "text/html" in /etc/nginx/conf.d/default.conf:146
2025/07/25 12:15:54 [warn] 3913#3913: duplicate MIME type "text/html" in /etc/nginx/conf.d/default.conf:146
2025/07/25 12:16:24 [warn] 3920#3920: duplicate MIME type "text/html" in /etc/nginx/conf.d/default.conf:146
2025/07/25 12:16:54 [warn] 3927#3927: duplicate MIME type "text/html" in /etc/nginx/conf.d/default.conf:142
2025/07/25 12:17:04 [warn] 3934#3934: duplicate MIME type "text/html" in /etc/nginx/conf.d/default.conf:142
2025/07/25 12:17:04 [notice] 3934#3934: signal process started
2025/07/25 12:17:04 [notice] 1#1: signal 1 (SIGHUP) received from 3934, reconfiguring
2025/07/25 12:17:04 [notice] 1#1: reconfiguring
2025/07/25 12:17:04 [warn] 1#1: duplicate MIME type "text/html" in /etc/nginx/conf.d/default.conf:142
2025/07/25 12:17:04 [notice] 1#1: using the "epoll" event method
2025/07/25 12:17:04 [notice] 1#1: start worker processes
2025/07/25 12:17:04 [notice] 1#1: start worker process 3941
2025/07/25 12:17:04 [notice] 1#1: start worker process 3942
2025/07/25 12:17:04 [notice] 3858#3858: gracefully shutting down
2025/07/25 12:17:04 [notice] 3859#3859: gracefully shutting down
2025/07/25 12:17:04 [notice] 3859#3859: exiting
2025/07/25 12:17:04 [notice] 3858#3858: exiting
2025/07/25 12:17:04 [notice] 3858#3858: exit
2025/07/25 12:17:04 [notice] 3859#3859: exit
2025/07/25 12:17:04 [notice] 1#1: signal 17 (SIGCHLD) received from 3859
2025/07/25 12:17:04 [notice] 1#1: worker process 3858 exited with code 0
2025/07/25 12:17:04 [notice] 1#1: worker process 3859 exited with code 0
2025/07/25 12:17:04 [notice] 1#1: signal 29 (SIGIO) received
2025/07/25 12:17:04 [notice] 1#1: signal 17 (SIGCHLD) received from 3858
2025/07/25 12:17:24 [warn] 3943#3943: duplicate MIME type "text/html" in /etc/nginx/conf.d/default.conf:142
2025/07/25 12:17:54 [warn] 3949#3949: duplicate MIME type "text/html" in /etc/nginx/conf.d/default.conf:142
2025/07/25 12:17:56 [warn] 3941#3941: *647 an upstream response is buffered to a temporary file /var/cache/nginx/proxy_temp/0/03/0000000030 while reading upstream, client: **********, server: liangliangdamowang.edu.deal, request: "GET /assets/react-core-DtMAePju.js HTTP/2.0", upstream: "http://**********:3000/assets/react-core-DtMAePju.js", host: "liangliangdamowang.edu.deal", referrer: "https://liangliangdamowang.edu.deal/ai/console"
2025/07/25 12:17:56 [warn] 3941#3941: *647 an upstream response is buffered to a temporary file /var/cache/nginx/proxy_temp/1/03/0000000031 while reading upstream, client: **********, server: liangliangdamowang.edu.deal, request: "GET /assets/index-BbPB7pPU.js HTTP/2.0", upstream: "http://**********:3000/assets/index-BbPB7pPU.js", host: "liangliangdamowang.edu.deal", referrer: "https://liangliangdamowang.edu.deal/ai/console"
2025/07/25 12:17:56 [warn] 3941#3941: *647 an upstream response is buffered to a temporary file /var/cache/nginx/proxy_temp/2/03/0000000032 while reading upstream, client: **********, server: liangliangdamowang.edu.deal, request: "GET /assets/semi-ui-BN13Wcpw.js HTTP/2.0", upstream: "http://**********:3000/assets/semi-ui-BN13Wcpw.js", host: "liangliangdamowang.edu.deal", referrer: "https://liangliangdamowang.edu.deal/ai/console"
2025/07/25 12:17:56 [warn] 3941#3941: *647 an upstream response is buffered to a temporary file /var/cache/nginx/proxy_temp/3/03/0000000033 while reading upstream, client: **********, server: liangliangdamowang.edu.deal, request: "GET /assets/semi-ui-yRXI6evF.css HTTP/2.0", upstream: "http://**********:3000/assets/semi-ui-yRXI6evF.css", host: "liangliangdamowang.edu.deal", referrer: "https://liangliangdamowang.edu.deal/ai/console"
2025/07/25 12:18:24 [warn] 3956#3956: duplicate MIME type "text/html" in /etc/nginx/conf.d/default.conf:143
2025/07/25 12:18:54 [warn] 3963#3963: duplicate MIME type "text/html" in /etc/nginx/conf.d/default.conf:143
2025/07/25 12:19:15 [warn] 3970#3970: duplicate MIME type "text/html" in /etc/nginx/conf.d/default.conf:142
2025/07/25 12:19:15 [notice] 3970#3970: signal process started
2025/07/25 12:19:15 [notice] 1#1: signal 1 (SIGHUP) received from 3970, reconfiguring
2025/07/25 12:19:15 [notice] 1#1: reconfiguring
2025/07/25 12:19:15 [warn] 1#1: duplicate MIME type "text/html" in /etc/nginx/conf.d/default.conf:142
2025/07/25 12:19:15 [notice] 1#1: using the "epoll" event method
2025/07/25 12:19:15 [notice] 1#1: start worker processes
2025/07/25 12:19:15 [notice] 1#1: start worker process 3976
2025/07/25 12:19:15 [notice] 1#1: start worker process 3977
2025/07/25 12:19:15 [notice] 3942#3942: gracefully shutting down
2025/07/25 12:19:15 [notice] 3942#3942: exiting
2025/07/25 12:19:15 [notice] 3941#3941: gracefully shutting down
2025/07/25 12:19:15 [notice] 3941#3941: exiting
2025/07/25 12:19:15 [notice] 3942#3942: exit
2025/07/25 12:19:15 [notice] 3941#3941: exit
2025/07/25 12:19:15 [notice] 1#1: signal 17 (SIGCHLD) received from 3941
2025/07/25 12:19:15 [notice] 1#1: worker process 3941 exited with code 0
2025/07/25 12:19:15 [notice] 1#1: signal 29 (SIGIO) received
2025/07/25 12:19:15 [notice] 1#1: signal 17 (SIGCHLD) received from 3942
2025/07/25 12:19:15 [notice] 1#1: worker process 3942 exited with code 0
2025/07/25 12:19:15 [notice] 1#1: signal 29 (SIGIO) received
2025/07/25 12:19:24 [warn] 3978#3978: duplicate MIME type "text/html" in /etc/nginx/conf.d/default.conf:142
2025/07/25 12:19:54 [warn] 3984#3984: duplicate MIME type "text/html" in /etc/nginx/conf.d/default.conf:142
2025/07/25 12:20:47 [notice] 3998#3998: signal process started
2025/07/25 12:20:47 [notice] 1#1: signal 1 (SIGHUP) received from 3998, reconfiguring
2025/07/25 12:20:47 [notice] 1#1: reconfiguring
2025/07/25 12:20:47 [notice] 1#1: using the "epoll" event method
2025/07/25 12:20:47 [notice] 1#1: start worker processes
2025/07/25 12:20:47 [notice] 1#1: start worker process 4005
2025/07/25 12:20:47 [notice] 1#1: start worker process 4006
2025/07/25 12:20:47 [notice] 3977#3977: gracefully shutting down
2025/07/25 12:20:47 [notice] 3977#3977: exiting
2025/07/25 12:20:47 [notice] 3976#3976: gracefully shutting down
2025/07/25 12:20:47 [notice] 3976#3976: exiting
2025/07/25 12:20:47 [notice] 3977#3977: exit
2025/07/25 12:20:47 [notice] 3976#3976: exit
2025/07/25 12:20:47 [notice] 1#1: signal 17 (SIGCHLD) received from 3977
2025/07/25 12:20:47 [notice] 1#1: worker process 3976 exited with code 0
2025/07/25 12:20:47 [notice] 1#1: worker process 3977 exited with code 0
2025/07/25 12:20:47 [notice] 1#1: signal 29 (SIGIO) received
2025/07/25 12:21:23 [warn] 4005#4005: *672 an upstream response is buffered to a temporary file /var/cache/nginx/proxy_temp/4/03/0000000034 while reading upstream, client: **********, server: liangliangdamowang.edu.deal, request: "GET /ai/assets/index-C8L2jEvp.css HTTP/2.0", upstream: "http://**********:3000/assets/index-C8L2jEvp.css", host: "liangliangdamowang.edu.deal", referrer: "https://liangliangdamowang.edu.deal/ai/console"
2025/07/25 12:21:23 [warn] 4005#4005: *672 an upstream response is buffered to a temporary file /var/cache/nginx/proxy_temp/5/03/0000000035 while reading upstream, client: **********, server: liangliangdamowang.edu.deal, request: "GET /ai/assets/semi-ui-BN13Wcpw.js HTTP/2.0", upstream: "http://**********:3000/assets/semi-ui-BN13Wcpw.js", host: "liangliangdamowang.edu.deal", referrer: "https://liangliangdamowang.edu.deal/ai/console"
2025/07/25 12:21:23 [warn] 4005#4005: *672 an upstream response is buffered to a temporary file /var/cache/nginx/proxy_temp/6/03/0000000036 while reading upstream, client: **********, server: liangliangdamowang.edu.deal, request: "GET /ai/assets/semi-ui-yRXI6evF.css HTTP/2.0", upstream: "http://**********:3000/assets/semi-ui-yRXI6evF.css", host: "liangliangdamowang.edu.deal", referrer: "https://liangliangdamowang.edu.deal/ai/console"
2025/07/25 12:21:23 [warn] 4005#4005: *672 an upstream response is buffered to a temporary file /var/cache/nginx/proxy_temp/7/03/0000000037 while reading upstream, client: **********, server: liangliangdamowang.edu.deal, request: "GET /ai/assets/index-BbPB7pPU.js HTTP/2.0", upstream: "http://**********:3000/assets/index-BbPB7pPU.js", host: "liangliangdamowang.edu.deal", referrer: "https://liangliangdamowang.edu.deal/ai/console"
2025/07/25 12:21:23 [warn] 4005#4005: *672 an upstream response is buffered to a temporary file /var/cache/nginx/proxy_temp/8/03/0000000038 while reading upstream, client: **********, server: liangliangdamowang.edu.deal, request: "GET /ai/assets/react-core-DtMAePju.js HTTP/2.0", upstream: "http://**********:3000/assets/react-core-DtMAePju.js", host: "liangliangdamowang.edu.deal", referrer: "https://liangliangdamowang.edu.deal/ai/console"
2025/07/25 12:33:57 [notice] 1#1: signal 3 (SIGQUIT) received, shutting down
2025/07/25 12:33:57 [notice] 4005#4005: gracefully shutting down
2025/07/25 12:33:57 [notice] 4005#4005: exiting
2025/07/25 12:33:57 [notice] 4006#4006: gracefully shutting down
2025/07/25 12:33:57 [notice] 4006#4006: exiting
2025/07/25 12:33:57 [notice] 4006#4006: exit
2025/07/25 12:33:57 [notice] 4005#4005: exit
2025/07/25 12:33:57 [notice] 1#1: signal 17 (SIGCHLD) received from 4006
2025/07/25 12:33:57 [notice] 1#1: worker process 4005 exited with code 0
2025/07/25 12:33:57 [notice] 1#1: worker process 4006 exited with code 0
2025/07/25 12:33:57 [notice] 1#1: exit
2025/07/25 12:34:42 [notice] 1#1: using the "epoll" event method
2025/07/25 12:34:42 [notice] 1#1: nginx/1.29.0
2025/07/25 12:34:42 [notice] 1#1: built by gcc 14.2.0 (Alpine 14.2.0) 
2025/07/25 12:34:42 [notice] 1#1: OS: Linux 6.1.0-37-cloud-amd64
2025/07/25 12:34:42 [notice] 1#1: getrlimit(RLIMIT_NOFILE): 1048576:1048576
2025/07/25 12:34:42 [notice] 1#1: start worker processes
2025/07/25 12:34:42 [notice] 1#1: start worker process 21
2025/07/25 12:34:42 [notice] 1#1: start worker process 22
2025/07/25 12:36:15 [warn] 21#21: *11 an upstream response is buffered to a temporary file /var/cache/nginx/proxy_temp/1/00/0000000001 while reading upstream, client: ***************, server: liangliangdamowang.edu.deal, request: "GET /assets/semi-ui-BN13Wcpw.js HTTP/2.0", upstream: "http://**********:3000/assets/semi-ui-BN13Wcpw.js", host: "liangliangdamowang.edu.deal", referrer: "https://liangliangdamowang.edu.deal/ai/console"
2025/07/25 12:36:15 [warn] 21#21: *11 an upstream response is buffered to a temporary file /var/cache/nginx/proxy_temp/2/00/0000000002 while reading upstream, client: ***************, server: liangliangdamowang.edu.deal, request: "GET /assets/index-BbPB7pPU.js HTTP/2.0", upstream: "http://**********:3000/assets/index-BbPB7pPU.js", host: "liangliangdamowang.edu.deal", referrer: "https://liangliangdamowang.edu.deal/ai/console"
2025/07/25 12:36:15 [warn] 21#21: *11 an upstream response is buffered to a temporary file /var/cache/nginx/proxy_temp/3/00/0000000003 while reading upstream, client: ***************, server: liangliangdamowang.edu.deal, request: "GET /assets/react-core-DtMAePju.js HTTP/2.0", upstream: "http://**********:3000/assets/react-core-DtMAePju.js", host: "liangliangdamowang.edu.deal", referrer: "https://liangliangdamowang.edu.deal/ai/console"
2025/07/25 12:36:15 [warn] 21#21: *11 an upstream response is buffered to a temporary file /var/cache/nginx/proxy_temp/4/00/0000000004 while reading upstream, client: ***************, server: liangliangdamowang.edu.deal, request: "GET /assets/index-C8L2jEvp.css HTTP/2.0", upstream: "http://**********:3000/assets/index-C8L2jEvp.css", host: "liangliangdamowang.edu.deal", referrer: "https://liangliangdamowang.edu.deal/ai/console"
2025/07/25 12:36:15 [warn] 21#21: *11 an upstream response is buffered to a temporary file /var/cache/nginx/proxy_temp/5/00/0000000005 while reading upstream, client: ***************, server: liangliangdamowang.edu.deal, request: "GET /assets/semi-ui-yRXI6evF.css HTTP/2.0", upstream: "http://**********:3000/assets/semi-ui-yRXI6evF.css", host: "liangliangdamowang.edu.deal", referrer: "https://liangliangdamowang.edu.deal/ai/console"
2025/07/25 12:37:29 [notice] 1#1: signal 3 (SIGQUIT) received, shutting down
2025/07/25 12:37:29 [notice] 22#22: gracefully shutting down
2025/07/25 12:37:29 [notice] 22#22: exiting
2025/07/25 12:37:29 [notice] 21#21: gracefully shutting down
2025/07/25 12:37:29 [notice] 21#21: exiting
2025/07/25 12:37:29 [notice] 21#21: exit
2025/07/25 12:37:29 [notice] 22#22: exit
2025/07/25 12:37:29 [notice] 1#1: signal 17 (SIGCHLD) received from 21
2025/07/25 12:37:29 [notice] 1#1: worker process 21 exited with code 0
2025/07/25 12:37:29 [notice] 1#1: worker process 22 exited with code 0
2025/07/25 12:37:29 [notice] 1#1: exit
2025/07/25 12:37:30 [notice] 1#1: using the "epoll" event method
2025/07/25 12:37:30 [notice] 1#1: nginx/1.29.0
2025/07/25 12:37:30 [notice] 1#1: built by gcc 14.2.0 (Alpine 14.2.0) 
2025/07/25 12:37:30 [notice] 1#1: OS: Linux 6.1.0-37-cloud-amd64
2025/07/25 12:37:30 [notice] 1#1: getrlimit(RLIMIT_NOFILE): 1048576:1048576
2025/07/25 12:37:30 [notice] 1#1: start worker processes
2025/07/25 12:37:30 [notice] 1#1: start worker process 22
2025/07/25 12:37:30 [notice] 1#1: start worker process 23
2025/07/25 12:38:05 [emerg] 31#31: host not found in upstream "new-api" in /etc/nginx/conf.d/default.conf:39
2025/07/25 12:39:40 [emerg] 50#50: host not found in upstream "new-api" in /etc/nginx/conf.d/default.conf:39
2025/07/25 12:40:16 [emerg] 56#56: host not found in upstream "new-api" in /etc/nginx/conf.d/default.conf:39
2025/07/25 12:40:51 [emerg] 62#62: host not found in upstream "new-api" in /etc/nginx/conf.d/default.conf:39
2025/07/25 12:41:26 [emerg] 68#68: host not found in upstream "new-api" in /etc/nginx/conf.d/default.conf:39
2025/07/25 12:42:01 [emerg] 74#74: host not found in upstream "new-api" in /etc/nginx/conf.d/default.conf:39
2025/07/25 12:42:37 [emerg] 79#79: host not found in upstream "new-api" in /etc/nginx/conf.d/default.conf:39
2025/07/25 12:43:12 [emerg] 84#84: host not found in upstream "new-api" in /etc/nginx/conf.d/default.conf:39
2025/07/25 12:43:47 [emerg] 91#91: host not found in upstream "new-api" in /etc/nginx/conf.d/default.conf:39
2025/07/25 12:44:22 [emerg] 98#98: host not found in upstream "new-api" in /etc/nginx/conf.d/default.conf:39
2025/07/25 12:44:57 [emerg] 104#104: host not found in upstream "new-api" in /etc/nginx/conf.d/default.conf:39
2025/07/25 12:45:32 [emerg] 110#110: host not found in upstream "new-api" in /etc/nginx/conf.d/default.conf:39
2025/07/25 12:46:07 [emerg] 116#116: host not found in upstream "new-api" in /etc/nginx/conf.d/default.conf:39
2025/07/25 12:46:43 [emerg] 121#121: host not found in upstream "new-api" in /etc/nginx/conf.d/default.conf:39
2025/07/25 12:47:18 [emerg] 126#126: host not found in upstream "new-api" in /etc/nginx/conf.d/default.conf:39
2025/07/25 12:47:53 [emerg] 131#131: host not found in upstream "new-api" in /etc/nginx/conf.d/default.conf:39
2025/07/25 12:48:28 [emerg] 137#137: host not found in upstream "new-api" in /etc/nginx/conf.d/default.conf:39
2025/07/25 12:49:03 [emerg] 144#144: host not found in upstream "new-api" in /etc/nginx/conf.d/default.conf:39
2025/07/25 12:49:38 [emerg] 150#150: host not found in upstream "new-api" in /etc/nginx/conf.d/default.conf:39
2025/07/25 12:56:44 [emerg] 239#239: "proxy_pass" cannot have URI part in location given by regular expression, or inside named location, or inside "if" statement, or inside "limit_except" block in /etc/nginx/conf.d/default.conf:106
2025/07/25 12:57:03 [notice] 1#1: signal 3 (SIGQUIT) received, shutting down
2025/07/25 12:57:03 [notice] 22#22: gracefully shutting down
2025/07/25 12:57:03 [notice] 22#22: exiting
2025/07/25 12:57:03 [notice] 22#22: exit
2025/07/25 12:57:03 [notice] 23#23: gracefully shutting down
2025/07/25 12:57:03 [notice] 23#23: exiting
2025/07/25 12:57:03 [notice] 23#23: exit
2025/07/25 12:57:03 [notice] 1#1: signal 17 (SIGCHLD) received from 22
2025/07/25 12:57:03 [notice] 1#1: worker process 22 exited with code 0
2025/07/25 12:57:03 [notice] 1#1: signal 29 (SIGIO) received
2025/07/25 12:57:03 [notice] 1#1: signal 17 (SIGCHLD) received from 23
2025/07/25 12:57:03 [notice] 1#1: worker process 23 exited with code 0
2025/07/25 12:57:03 [notice] 1#1: exit
2025/07/25 12:57:03 [emerg] 1#1: "proxy_pass" cannot have URI part in location given by regular expression, or inside named location, or inside "if" statement, or inside "limit_except" block in /etc/nginx/conf.d/default.conf:106
2025/07/25 12:57:04 [emerg] 1#1: "proxy_pass" cannot have URI part in location given by regular expression, or inside named location, or inside "if" statement, or inside "limit_except" block in /etc/nginx/conf.d/default.conf:106
2025/07/25 12:57:05 [emerg] 1#1: "proxy_pass" cannot have URI part in location given by regular expression, or inside named location, or inside "if" statement, or inside "limit_except" block in /etc/nginx/conf.d/default.conf:106
2025/07/25 12:57:06 [emerg] 1#1: "proxy_pass" cannot have URI part in location given by regular expression, or inside named location, or inside "if" statement, or inside "limit_except" block in /etc/nginx/conf.d/default.conf:106
2025/07/25 12:57:07 [emerg] 1#1: "proxy_pass" cannot have URI part in location given by regular expression, or inside named location, or inside "if" statement, or inside "limit_except" block in /etc/nginx/conf.d/default.conf:106
2025/07/25 12:57:09 [emerg] 1#1: "proxy_pass" cannot have URI part in location given by regular expression, or inside named location, or inside "if" statement, or inside "limit_except" block in /etc/nginx/conf.d/default.conf:106
2025/07/25 12:57:12 [emerg] 1#1: "proxy_pass" cannot have URI part in location given by regular expression, or inside named location, or inside "if" statement, or inside "limit_except" block in /etc/nginx/conf.d/default.conf:106
2025/07/25 12:57:19 [emerg] 1#1: "proxy_pass" cannot have URI part in location given by regular expression, or inside named location, or inside "if" statement, or inside "limit_except" block in /etc/nginx/conf.d/default.conf:106
2025/07/25 12:57:32 [emerg] 1#1: "proxy_pass" cannot have URI part in location given by regular expression, or inside named location, or inside "if" statement, or inside "limit_except" block in /etc/nginx/conf.d/default.conf:106
2025/07/25 12:57:58 [notice] 1#1: using the "epoll" event method
2025/07/25 12:57:58 [notice] 1#1: nginx/1.29.0
2025/07/25 12:57:58 [notice] 1#1: built by gcc 14.2.0 (Alpine 14.2.0) 
2025/07/25 12:57:58 [notice] 1#1: OS: Linux 6.1.0-37-cloud-amd64
2025/07/25 12:57:58 [notice] 1#1: getrlimit(RLIMIT_NOFILE): 1048576:1048576
2025/07/25 12:57:58 [notice] 1#1: start worker processes
2025/07/25 12:57:58 [notice] 1#1: start worker process 21
2025/07/25 12:57:58 [notice] 1#1: start worker process 22
2025/07/25 12:58:09 [notice] 1#1: signal 3 (SIGQUIT) received, shutting down
2025/07/25 12:58:09 [notice] 21#21: gracefully shutting down
2025/07/25 12:58:09 [notice] 22#22: gracefully shutting down
2025/07/25 12:58:09 [notice] 22#22: exiting
2025/07/25 12:58:09 [notice] 22#22: exit
2025/07/25 12:58:09 [notice] 1#1: signal 17 (SIGCHLD) received from 22
2025/07/25 12:58:09 [notice] 1#1: worker process 22 exited with code 0
2025/07/25 12:58:09 [notice] 1#1: signal 29 (SIGIO) received
2025/07/25 12:58:09 [notice] 21#21: exiting
2025/07/25 12:58:09 [notice] 21#21: exit
2025/07/25 12:58:09 [notice] 1#1: signal 17 (SIGCHLD) received from 21
2025/07/25 12:58:09 [notice] 1#1: worker process 21 exited with code 0
2025/07/25 12:58:09 [notice] 1#1: exit
2025/07/25 12:58:10 [notice] 1#1: using the "epoll" event method
2025/07/25 12:58:10 [notice] 1#1: nginx/1.29.0
2025/07/25 12:58:10 [notice] 1#1: built by gcc 14.2.0 (Alpine 14.2.0) 
2025/07/25 12:58:10 [notice] 1#1: OS: Linux 6.1.0-37-cloud-amd64
2025/07/25 12:58:10 [notice] 1#1: getrlimit(RLIMIT_NOFILE): 1048576:1048576
2025/07/25 12:58:10 [notice] 1#1: start worker processes
2025/07/25 12:58:10 [notice] 1#1: start worker process 22
2025/07/25 12:58:10 [notice] 1#1: start worker process 23
2025/07/25 12:59:12 [notice] 1#1: signal 3 (SIGQUIT) received, shutting down
2025/07/25 12:59:12 [notice] 22#22: gracefully shutting down
2025/07/25 12:59:12 [notice] 23#23: gracefully shutting down
2025/07/25 12:59:12 [notice] 23#23: exiting
2025/07/25 12:59:12 [notice] 23#23: exit
2025/07/25 12:59:12 [notice] 1#1: signal 17 (SIGCHLD) received from 23
2025/07/25 12:59:12 [notice] 1#1: worker process 23 exited with code 0
2025/07/25 12:59:12 [notice] 1#1: signal 29 (SIGIO) received
2025/07/25 12:59:13 [notice] 22#22: exiting
2025/07/25 12:59:13 [notice] 22#22: exit
2025/07/25 12:59:13 [notice] 1#1: signal 17 (SIGCHLD) received from 22
2025/07/25 12:59:13 [notice] 1#1: worker process 22 exited with code 0
2025/07/25 12:59:13 [notice] 1#1: exit
2025/07/25 12:59:15 [notice] 1#1: using the "epoll" event method
2025/07/25 12:59:15 [notice] 1#1: nginx/1.29.0
2025/07/25 12:59:15 [notice] 1#1: built by gcc 14.2.0 (Alpine 14.2.0) 
2025/07/25 12:59:15 [notice] 1#1: OS: Linux 6.1.0-37-cloud-amd64
2025/07/25 12:59:15 [notice] 1#1: getrlimit(RLIMIT_NOFILE): 1048576:1048576
2025/07/25 12:59:15 [notice] 1#1: start worker processes
2025/07/25 12:59:15 [notice] 1#1: start worker process 21
2025/07/25 12:59:15 [notice] 1#1: start worker process 22
2025/07/25 13:02:12 [warn] 21#21: *33 an upstream response is buffered to a temporary file /var/cache/nginx/proxy_temp/1/00/0000000001 while reading upstream, client: **************, server: liangliangdamowang.edu.deal, request: "GET /assets/index-C8L2jEvp.css HTTP/2.0", upstream: "http://**********:3000/assets/index-C8L2jEvp.css", host: "liangliangdamowang.edu.deal", referrer: "https://liangliangdamowang.edu.deal/ai/console"
2025/07/25 13:02:12 [warn] 21#21: *33 an upstream response is buffered to a temporary file /var/cache/nginx/proxy_temp/2/00/0000000002 while reading upstream, client: **************, server: liangliangdamowang.edu.deal, request: "GET /assets/index-BbPB7pPU.js HTTP/2.0", upstream: "http://**********:3000/assets/index-BbPB7pPU.js", host: "liangliangdamowang.edu.deal", referrer: "https://liangliangdamowang.edu.deal/ai/console"
2025/07/25 13:02:12 [warn] 21#21: *33 an upstream response is buffered to a temporary file /var/cache/nginx/proxy_temp/3/00/0000000003 while reading upstream, client: **************, server: liangliangdamowang.edu.deal, request: "GET /assets/semi-ui-yRXI6evF.css HTTP/2.0", upstream: "http://**********:3000/assets/semi-ui-yRXI6evF.css", host: "liangliangdamowang.edu.deal", referrer: "https://liangliangdamowang.edu.deal/ai/console"
2025/07/25 13:02:12 [warn] 21#21: *33 an upstream response is buffered to a temporary file /var/cache/nginx/proxy_temp/4/00/0000000004 while reading upstream, client: **************, server: liangliangdamowang.edu.deal, request: "GET /assets/semi-ui-BN13Wcpw.js HTTP/2.0", upstream: "http://**********:3000/assets/semi-ui-BN13Wcpw.js", host: "liangliangdamowang.edu.deal", referrer: "https://liangliangdamowang.edu.deal/ai/console"
2025/07/25 13:12:36 [notice] 1#1: signal 3 (SIGQUIT) received, shutting down
2025/07/25 13:12:36 [notice] 22#22: gracefully shutting down
2025/07/25 13:12:36 [notice] 22#22: exiting
2025/07/25 13:12:36 [notice] 21#21: gracefully shutting down
2025/07/25 13:12:36 [notice] 21#21: exiting
2025/07/25 13:12:36 [notice] 22#22: exit
2025/07/25 13:12:36 [notice] 21#21: exit
2025/07/25 13:12:36 [notice] 1#1: signal 17 (SIGCHLD) received from 22
2025/07/25 13:12:36 [notice] 1#1: worker process 22 exited with code 0
2025/07/25 13:12:36 [notice] 1#1: signal 29 (SIGIO) received
2025/07/25 13:12:36 [notice] 1#1: signal 17 (SIGCHLD) received from 21
2025/07/25 13:12:36 [notice] 1#1: worker process 21 exited with code 0
2025/07/25 13:12:36 [notice] 1#1: exit
2025/07/25 13:12:37 [notice] 1#1: using the "epoll" event method
2025/07/25 13:12:37 [notice] 1#1: nginx/1.29.0
2025/07/25 13:12:37 [notice] 1#1: built by gcc 14.2.0 (Alpine 14.2.0) 
2025/07/25 13:12:37 [notice] 1#1: OS: Linux 6.1.0-37-cloud-amd64
2025/07/25 13:12:37 [notice] 1#1: getrlimit(RLIMIT_NOFILE): 1048576:1048576
2025/07/25 13:12:37 [notice] 1#1: start worker processes
2025/07/25 13:12:37 [notice] 1#1: start worker process 22
2025/07/25 13:12:37 [notice] 1#1: start worker process 23
2025/07/25 15:38:34 [error] 22#22: *199 directory index of "/var/www/love/" is forbidden, client: ***************, server: liangliangdamowang.edu.deal, request: "GET /love/ HTTP/2.0", host: "liangliangdamowang.edu.deal"
2025/07/25 15:38:36 [error] 22#22: *199 directory index of "/var/www/love/" is forbidden, client: ***************, server: liangliangdamowang.edu.deal, request: "GET /love/ HTTP/2.0", host: "liangliangdamowang.edu.deal"
2025/07/25 15:58:51 [error] 22#22: *237 directory index of "/var/www/love/" is forbidden, client: **********, server: liangliangdamowang.edu.deal, request: "HEAD /love/ HTTP/2.0", host: "localhost"
2025/07/25 16:00:38 [notice] 1#1: signal 3 (SIGQUIT) received, shutting down
2025/07/25 16:00:38 [notice] 22#22: gracefully shutting down
2025/07/25 16:00:38 [notice] 23#23: gracefully shutting down
2025/07/25 16:00:38 [notice] 22#22: exiting
2025/07/25 16:00:38 [notice] 23#23: exiting
2025/07/25 16:00:38 [notice] 23#23: exit
2025/07/25 16:00:38 [notice] 22#22: exit
2025/07/25 16:00:38 [notice] 1#1: signal 17 (SIGCHLD) received from 22
2025/07/25 16:00:38 [notice] 1#1: worker process 22 exited with code 0
2025/07/25 16:00:38 [notice] 1#1: signal 29 (SIGIO) received
2025/07/25 16:00:38 [notice] 1#1: signal 17 (SIGCHLD) received from 23
2025/07/25 16:00:38 [notice] 1#1: worker process 23 exited with code 0
2025/07/25 16:00:38 [notice] 1#1: exit
2025/07/26 06:03:39 [notice] 1#1: using the "epoll" event method
2025/07/26 06:03:39 [notice] 1#1: nginx/1.29.0
2025/07/26 06:03:39 [notice] 1#1: built by gcc 14.2.0 (Alpine 14.2.0) 
2025/07/26 06:03:39 [notice] 1#1: OS: Linux 6.1.0-37-cloud-amd64
2025/07/26 06:03:39 [notice] 1#1: getrlimit(RLIMIT_NOFILE): 1048576:1048576
2025/07/26 06:03:39 [notice] 1#1: start worker processes
2025/07/26 06:03:39 [notice] 1#1: start worker process 20
2025/07/26 06:03:39 [notice] 1#1: start worker process 21
2025/07/26 06:08:27 [error] 20#20: *37 rewrite or internal redirection cycle while internally redirecting to "/love/index.html", client: ***************, server: liangliangdamowang.edu.deal, request: "GET /love/background/%E6%B5%B7%E5%BA%95/%E6%B5%B7%E5%BA%95.mp4 HTTP/2.0", host: "liangliangdamowang.edu.deal", referrer: "https://liangliangdamowang.edu.deal/love/together-days"
2025/07/26 06:08:27 [error] 20#20: *37 rewrite or internal redirection cycle while internally redirecting to "/love/index.html", client: ***************, server: liangliangdamowang.edu.deal, request: "GET /love/background/%E6%B5%B7%E5%BA%95/%E6%B5%B7%E5%BA%95.mp4 HTTP/2.0", host: "liangliangdamowang.edu.deal", referrer: "https://liangliangdamowang.edu.deal/love/together-days"
2025/07/26 06:13:50 [notice] 1#1: signal 3 (SIGQUIT) received, shutting down
2025/07/26 06:13:50 [notice] 20#20: gracefully shutting down
2025/07/26 06:13:50 [notice] 20#20: exiting
2025/07/26 06:13:50 [notice] 20#20: exit
2025/07/26 06:13:50 [notice] 21#21: gracefully shutting down
2025/07/26 06:13:50 [notice] 21#21: exiting
2025/07/26 06:13:50 [notice] 21#21: exit
2025/07/26 06:13:50 [notice] 1#1: signal 17 (SIGCHLD) received from 20
2025/07/26 06:13:50 [notice] 1#1: worker process 20 exited with code 0
2025/07/26 06:13:50 [notice] 1#1: signal 29 (SIGIO) received
2025/07/26 06:13:50 [notice] 1#1: signal 17 (SIGCHLD) received from 21
2025/07/26 06:13:50 [notice] 1#1: worker process 21 exited with code 0
2025/07/26 06:13:50 [notice] 1#1: exit
2025/07/26 06:13:51 [notice] 1#1: using the "epoll" event method
2025/07/26 06:13:51 [notice] 1#1: nginx/1.29.0
2025/07/26 06:13:51 [notice] 1#1: built by gcc 14.2.0 (Alpine 14.2.0) 
2025/07/26 06:13:51 [notice] 1#1: OS: Linux 6.1.0-37-cloud-amd64
2025/07/26 06:13:51 [notice] 1#1: getrlimit(RLIMIT_NOFILE): 1048576:1048576
2025/07/26 06:13:51 [notice] 1#1: start worker processes
2025/07/26 06:13:51 [notice] 1#1: start worker process 22
2025/07/26 06:13:51 [notice] 1#1: start worker process 23
2025/07/26 06:16:18 [notice] 1#1: signal 15 (SIGTERM) received, exiting
2025/07/26 06:16:18 [notice] 23#23: signal 15 (SIGTERM) received, exiting
2025/07/26 06:16:18 [notice] 23#23: exiting
2025/07/26 06:16:18 [notice] 22#22: signal 15 (SIGTERM) received, exiting
2025/07/26 06:16:18 [notice] 22#22: exiting
2025/07/26 06:16:18 [notice] 22#22: exit
2025/07/26 06:16:18 [notice] 23#23: exit
2025/07/26 06:16:18 [notice] 1#1: signal 17 (SIGCHLD) received from 22
2025/07/26 06:16:18 [notice] 1#1: worker process 22 exited with code 0
2025/07/26 06:16:18 [notice] 1#1: signal 29 (SIGIO) received
2025/07/26 06:16:18 [notice] 1#1: signal 17 (SIGCHLD) received from 23
2025/07/26 06:16:18 [notice] 1#1: worker process 23 exited with code 0
2025/07/26 06:16:18 [notice] 1#1: exit
2025/07/26 06:16:19 [notice] 1#1: using the "epoll" event method
2025/07/26 06:16:19 [notice] 1#1: nginx/1.29.0
2025/07/26 06:16:19 [notice] 1#1: built by gcc 14.2.0 (Alpine 14.2.0) 
2025/07/26 06:16:19 [notice] 1#1: OS: Linux 6.1.0-37-cloud-amd64
2025/07/26 06:16:19 [notice] 1#1: getrlimit(RLIMIT_NOFILE): 1048576:1048576
2025/07/26 06:16:19 [notice] 1#1: start worker processes
2025/07/26 06:16:19 [notice] 1#1: start worker process 21
2025/07/26 06:16:19 [notice] 1#1: start worker process 22
2025/07/26 06:16:46 [notice] 1#1: signal 3 (SIGQUIT) received, shutting down
2025/07/26 06:16:46 [notice] 21#21: gracefully shutting down
2025/07/26 06:16:46 [notice] 21#21: exiting
2025/07/26 06:16:46 [notice] 22#22: gracefully shutting down
2025/07/26 06:16:46 [notice] 22#22: exiting
2025/07/26 06:16:46 [notice] 22#22: exit
2025/07/26 06:16:46 [notice] 21#21: exit
2025/07/26 06:16:46 [notice] 1#1: signal 17 (SIGCHLD) received from 21
2025/07/26 06:16:46 [notice] 1#1: worker process 21 exited with code 0
2025/07/26 06:16:46 [notice] 1#1: signal 29 (SIGIO) received
2025/07/26 06:16:46 [notice] 1#1: signal 17 (SIGCHLD) received from 22
2025/07/26 06:16:46 [notice] 1#1: worker process 22 exited with code 0
2025/07/26 06:16:46 [notice] 1#1: exit
2025/07/26 06:16:48 [notice] 1#1: using the "epoll" event method
2025/07/26 06:16:48 [notice] 1#1: nginx/1.29.0
2025/07/26 06:16:48 [notice] 1#1: built by gcc 14.2.0 (Alpine 14.2.0) 
2025/07/26 06:16:48 [notice] 1#1: OS: Linux 6.1.0-37-cloud-amd64
2025/07/26 06:16:48 [notice] 1#1: getrlimit(RLIMIT_NOFILE): 1048576:1048576
2025/07/26 06:16:48 [notice] 1#1: start worker processes
2025/07/26 06:16:48 [notice] 1#1: start worker process 21
2025/07/26 06:16:48 [notice] 1#1: start worker process 22
2025/07/26 06:17:15 [notice] 1#1: signal 3 (SIGQUIT) received, shutting down
2025/07/26 06:17:15 [notice] 21#21: gracefully shutting down
2025/07/26 06:17:15 [notice] 22#22: gracefully shutting down
2025/07/26 06:17:15 [notice] 21#21: exiting
2025/07/26 06:17:15 [notice] 22#22: exiting
2025/07/26 06:17:15 [notice] 22#22: exit
2025/07/26 06:17:15 [notice] 21#21: exit
2025/07/26 06:17:15 [notice] 1#1: signal 17 (SIGCHLD) received from 22
2025/07/26 06:17:15 [notice] 1#1: worker process 21 exited with code 0
2025/07/26 06:17:15 [notice] 1#1: worker process 22 exited with code 0
2025/07/26 06:17:15 [notice] 1#1: exit
2025/07/26 06:17:39 [notice] 1#1: using the "epoll" event method
2025/07/26 06:17:39 [notice] 1#1: nginx/1.29.0
2025/07/26 06:17:39 [notice] 1#1: built by gcc 14.2.0 (Alpine 14.2.0) 
2025/07/26 06:17:39 [notice] 1#1: OS: Linux 6.1.0-37-cloud-amd64
2025/07/26 06:17:39 [notice] 1#1: getrlimit(RLIMIT_NOFILE): 1048576:1048576
2025/07/26 06:17:39 [notice] 1#1: start worker processes
2025/07/26 06:17:39 [notice] 1#1: start worker process 21
2025/07/26 06:17:39 [notice] 1#1: start worker process 22
2025/07/26 06:21:07 [notice] 1#1: signal 3 (SIGQUIT) received, shutting down
2025/07/26 06:21:07 [notice] 21#21: gracefully shutting down
2025/07/26 06:21:07 [notice] 22#22: gracefully shutting down
2025/07/26 06:21:07 [notice] 22#22: exiting
2025/07/26 06:21:07 [notice] 21#21: exiting
2025/07/26 06:21:07 [notice] 22#22: exit
2025/07/26 06:21:07 [notice] 21#21: exit
2025/07/26 06:21:07 [notice] 1#1: signal 17 (SIGCHLD) received from 21
2025/07/26 06:21:07 [notice] 1#1: worker process 21 exited with code 0
2025/07/26 06:21:07 [notice] 1#1: signal 29 (SIGIO) received
2025/07/26 06:21:07 [notice] 1#1: signal 17 (SIGCHLD) received from 22
2025/07/26 06:21:07 [notice] 1#1: worker process 22 exited with code 0
2025/07/26 06:21:07 [notice] 1#1: exit
2025/07/26 06:21:09 [notice] 1#1: using the "epoll" event method
2025/07/26 06:21:09 [notice] 1#1: nginx/1.29.0
2025/07/26 06:21:09 [notice] 1#1: built by gcc 14.2.0 (Alpine 14.2.0) 
2025/07/26 06:21:09 [notice] 1#1: OS: Linux 6.1.0-37-cloud-amd64
2025/07/26 06:21:09 [notice] 1#1: getrlimit(RLIMIT_NOFILE): 1048576:1048576
2025/07/26 06:21:09 [notice] 1#1: start worker processes
2025/07/26 06:21:09 [notice] 1#1: start worker process 20
2025/07/26 06:21:09 [notice] 1#1: start worker process 21
2025/07/26 06:25:48 [notice] 1#1: signal 3 (SIGQUIT) received, shutting down
2025/07/26 06:25:48 [notice] 20#20: gracefully shutting down
2025/07/26 06:25:48 [notice] 20#20: exiting
2025/07/26 06:25:48 [notice] 21#21: gracefully shutting down
2025/07/26 06:25:48 [notice] 21#21: exiting
2025/07/26 06:25:48 [notice] 20#20: exit
2025/07/26 06:25:48 [notice] 21#21: exit
2025/07/26 06:25:48 [notice] 1#1: signal 17 (SIGCHLD) received from 21
2025/07/26 06:25:48 [notice] 1#1: worker process 21 exited with code 0
2025/07/26 06:25:48 [notice] 1#1: signal 29 (SIGIO) received
2025/07/26 06:25:48 [notice] 1#1: signal 17 (SIGCHLD) received from 20
2025/07/26 06:25:48 [notice] 1#1: worker process 20 exited with code 0
2025/07/26 06:25:48 [notice] 1#1: exit
2025/07/26 06:25:49 [emerg] 1#1: host not found in upstream "new-api" in /etc/nginx/conf.d/default.conf:40
2025/07/26 06:25:54 [notice] 1#1: using the "epoll" event method
2025/07/26 06:25:54 [notice] 1#1: nginx/1.29.0
2025/07/26 06:25:54 [notice] 1#1: built by gcc 14.2.0 (Alpine 14.2.0) 
2025/07/26 06:25:54 [notice] 1#1: OS: Linux 6.1.0-37-cloud-amd64
2025/07/26 06:25:54 [notice] 1#1: getrlimit(RLIMIT_NOFILE): 1048576:1048576
2025/07/26 06:25:54 [notice] 1#1: start worker processes
2025/07/26 06:25:54 [notice] 1#1: start worker process 20
2025/07/26 06:25:54 [notice] 1#1: start worker process 21
2025/07/26 06:28:17 [notice] 1#1: signal 3 (SIGQUIT) received, shutting down
2025/07/26 06:28:17 [notice] 21#21: gracefully shutting down
2025/07/26 06:28:17 [notice] 21#21: exiting
2025/07/26 06:28:17 [notice] 20#20: gracefully shutting down
2025/07/26 06:28:17 [notice] 20#20: exiting
2025/07/26 06:28:17 [notice] 21#21: exit
2025/07/26 06:28:17 [notice] 20#20: exit
2025/07/26 06:28:17 [notice] 1#1: signal 17 (SIGCHLD) received from 20
2025/07/26 06:28:17 [notice] 1#1: worker process 20 exited with code 0
2025/07/26 06:28:17 [notice] 1#1: signal 29 (SIGIO) received
2025/07/26 06:28:17 [notice] 1#1: signal 17 (SIGCHLD) received from 21
2025/07/26 06:28:17 [notice] 1#1: worker process 21 exited with code 0
2025/07/26 06:28:17 [notice] 1#1: exit
2025/07/26 06:28:18 [notice] 1#1: using the "epoll" event method
2025/07/26 06:28:18 [notice] 1#1: nginx/1.29.0
2025/07/26 06:28:18 [notice] 1#1: built by gcc 14.2.0 (Alpine 14.2.0) 
2025/07/26 06:28:18 [notice] 1#1: OS: Linux 6.1.0-37-cloud-amd64
2025/07/26 06:28:18 [notice] 1#1: getrlimit(RLIMIT_NOFILE): 1048576:1048576
2025/07/26 06:28:18 [notice] 1#1: start worker processes
2025/07/26 06:28:18 [notice] 1#1: start worker process 21
2025/07/26 06:28:18 [notice] 1#1: start worker process 22
2025/07/26 06:37:45 [notice] 1#1: signal 3 (SIGQUIT) received, shutting down
2025/07/26 06:37:45 [notice] 21#21: gracefully shutting down
2025/07/26 06:37:45 [notice] 21#21: exiting
2025/07/26 06:37:45 [notice] 22#22: gracefully shutting down
2025/07/26 06:37:45 [notice] 22#22: exiting
2025/07/26 06:37:45 [notice] 21#21: exit
2025/07/26 06:37:45 [notice] 22#22: exit
2025/07/26 06:37:45 [notice] 1#1: signal 17 (SIGCHLD) received from 22
2025/07/26 06:37:45 [notice] 1#1: worker process 21 exited with code 0
2025/07/26 06:37:45 [notice] 1#1: worker process 22 exited with code 0
2025/07/26 06:37:45 [notice] 1#1: exit
2025/07/26 06:37:45 [notice] 1#1: using the "epoll" event method
2025/07/26 06:37:45 [notice] 1#1: nginx/1.29.0
2025/07/26 06:37:45 [notice] 1#1: built by gcc 14.2.0 (Alpine 14.2.0) 
2025/07/26 06:37:45 [notice] 1#1: OS: Linux 6.1.0-37-cloud-amd64
2025/07/26 06:37:45 [notice] 1#1: getrlimit(RLIMIT_NOFILE): 1048576:1048576
2025/07/26 06:37:45 [notice] 1#1: start worker processes
2025/07/26 06:37:45 [notice] 1#1: start worker process 21
2025/07/26 06:37:45 [notice] 1#1: start worker process 22
