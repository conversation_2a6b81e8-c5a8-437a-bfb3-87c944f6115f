#!/bin/bash

# Nginx配置和Docker状态恢复脚本
# 创建时间: $(date)
# 备份目录: /root/workspace/shared/nginx/backup/task-backup-20250726_150544

set -e

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

print_info() {
    echo -e "${YELLOW}[信息]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[成功]${NC} $1"
}

print_error() {
    echo -e "${RED}[错误]${NC} $1"
}

BACKUP_DIR="/root/workspace/shared/nginx/backup/task-backup-20250726_150544"

print_info "开始恢复系统配置..."

# 1. 恢复nginx配置
print_info "恢复nginx配置文件..."
if [ -d "$BACKUP_DIR/nginx-conf.d-backup" ]; then
    cp -r "$BACKUP_DIR/nginx-conf.d-backup"/* /root/workspace/shared/nginx/conf.d/
    print_success "nginx配置文件已恢复"
else
    print_error "nginx配置备份不存在"
fi

# 2. 恢复docker-compose.yml
print_info "恢复docker-compose.yml..."
if [ -f "$BACKUP_DIR/docker-compose.yml.backup" ]; then
    cp "$BACKUP_DIR/docker-compose.yml.backup" /root/workspace/new-api/docker-compose.yml
    print_success "docker-compose.yml已恢复"
else
    print_error "docker-compose.yml备份不存在"
fi

# 3. 重启服务
print_info "重启Docker服务..."
cd /root/workspace/new-api
docker-compose down
docker-compose up -d

# 4. 测试nginx配置
print_info "测试nginx配置..."
if nginx -t; then
    print_success "nginx配置测试通过"
    systemctl reload nginx
    print_success "nginx服务已重载"
else
    print_error "nginx配置测试失败"
    exit 1
fi

print_success "系统配置恢复完成！"

# 显示恢复信息
print_info "恢复的配置信息："
echo "- nginx配置文件: /root/workspace/shared/nginx/conf.d/"
echo "- docker-compose.yml: /root/workspace/new-api/docker-compose.yml"
echo "- Docker容器状态: 已重启"
echo "- nginx服务: 已重载"

print_info "如需查看备份的状态信息："
echo "- Docker容器状态: $BACKUP_DIR/docker-containers.txt"
echo "- Docker网络信息: $BACKUP_DIR/docker-networks.txt"
echo "- 容器IP信息: $BACKUP_DIR/container-ip-info.txt"
