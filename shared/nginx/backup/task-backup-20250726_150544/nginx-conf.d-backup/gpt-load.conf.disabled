# GPT-Load Project Configuration
# Domain: liangliangdamowang.edu.deal
# Backend: 127.0.0.1:3001
# Path: /lgpt-load/

# Upstream for gpt-load backend
upstream gpt_load_backend {
    server 127.0.0.1:3001;
    keepalive 32;
}

# GPT-Load site configuration (part of main server block)
# Note: This file contains location blocks to be included in the main server

# ===== GPT-LOAD CONFIGURATIONS ===== ✨ GPT-Load AI代理服务

# Handle /lgpt-load without trailing slash
location = /lgpt-load {
    return 301 $scheme://$host/lgpt-load/;
}

# GPT-Load static assets (CSS, JS, images)
location /lgpt-load/assets/ {
    # Remove /lgpt-load prefix before forwarding to backend
    rewrite ^/lgpt-load/(.*) /$1 break;
    
    # Forward to gpt-load backend
    proxy_pass http://gpt_load_backend;
    
    # Include proxy parameters
    include /root/workspace/shared/nginx/snippets/proxy-params.conf;
    
    # Cache static assets
    expires 1y;
    add_header Cache-Control "public, immutable";
}

# GPT-Load API routes
location /lgpt-load/api/ {
    # Remove /lgpt-load prefix before forwarding to backend
    rewrite ^/lgpt-load/(.*) /$1 break;
    
    # Forward to gpt-load backend
    proxy_pass http://gpt_load_backend;
    
    # Include proxy parameters
    include /root/workspace/shared/nginx/snippets/proxy-params.conf;
    
    # Include CORS headers for API
    include /root/workspace/shared/nginx/snippets/cors-headers.conf;
    
    # Handle large request bodies
    client_max_body_size 100M;
}

# GPT-Load proxy routes
location /lgpt-load/proxy/ {
    # Remove /lgpt-load prefix before forwarding to backend
    rewrite ^/lgpt-load/(.*) /$1 break;
    
    # Forward to gpt-load backend
    proxy_pass http://gpt_load_backend;
    
    # Include proxy parameters
    include /root/workspace/shared/nginx/snippets/proxy-params.conf;
    
    # Include CORS headers for API
    include /root/workspace/shared/nginx/snippets/cors-headers.conf;
    
    # Handle large request bodies
    client_max_body_size 100M;
}

# GPT-Load health check
location /lgpt-load/health {
    # Remove /lgpt-load prefix before forwarding to backend
    rewrite ^/lgpt-load/(.*) /$1 break;
    
    # Forward to gpt-load backend
    proxy_pass http://gpt_load_backend;
    
    # Include proxy parameters
    include /root/workspace/shared/nginx/snippets/proxy-params.conf;
    
    access_log off;
}

# GPT-Load main interface
location /lgpt-load/ {
    # Remove /lgpt-load prefix before forwarding to backend
    rewrite ^/lgpt-load/(.*) /$1 break;
    
    # Forward to gpt-load backend
    proxy_pass http://gpt_load_backend;
    
    # Include proxy parameters
    include /root/workspace/shared/nginx/snippets/proxy-params.conf;
    
    # Handle large request bodies
    client_max_body_size 100M;
    
    # Fix asset paths in HTML for subpath deployment
    sub_filter 'href="/assets/' 'href="/lgpt-load/assets/';
    sub_filter 'src="/assets/' 'src="/lgpt-load/assets/';
    sub_filter '"/api/' '"/lgpt-load/api/';
    sub_filter "'/api/" "'/lgpt-load/api/";
    sub_filter '"/proxy/' '"/lgpt-load/proxy/';
    sub_filter "'/proxy/" "'/lgpt-load/proxy/";
    sub_filter '"/health' '"/lgpt-load/health';
    sub_filter "'/health" "'/lgpt-load/health";
    sub_filter_once off;
    
    # Security headers for GPT-Load
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
}
