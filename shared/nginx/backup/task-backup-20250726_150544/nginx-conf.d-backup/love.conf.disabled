# Love Project Configuration
# Domain: liangliangdamowang.edu.deal
# Backend: 127.0.0.1:1314
# Path: /love/

# Upstream for love site backend
upstream love_backend {
    server 127.0.0.1:1314;
    keepalive 32;
}

# Love site configuration (part of main server block)
# Note: This file contains location blocks to be included in the main server

# ===== LOVE SITE CONFIGURATIONS =====

# Handle /love without trailing slash
location = /love {
    return 301 $scheme://$host/love/;
}

# Love site main page
location = /love/ {
    alias /root/workspace/love/html/;
    try_files /index.html =404;
}

# Love site clean URLs (without .html extension)
location ~ ^/love/(together-days|anniversary|meetings|memorial)$ {
    alias /root/workspace/love/html/;
    try_files /$1.html =404;
}

# Love site HTML files (direct access)
location /love/html/ {
    alias /root/workspace/love/html/;
    try_files $uri $uri/ =404;

    # Include static file caching
    include /root/workspace/shared/nginx/snippets/cache-static.conf;
}

# Love site root files (CSS, JS, etc.)
location /love/style.css {
    alias /root/workspace/love/style.css;
    expires 1y;
    add_header Cache-Control "public, immutable";
}

location /love/pages.css {
    alias /root/workspace/love/pages.css;
    expires 1y;
    add_header Cache-Control "public, immutable";
}

location /love/script.js {
    alias /root/workspace/love/script.js;
    expires 1y;
    add_header Cache-Control "public, immutable";
}

location /love/romantic-quotes.js {
    alias /root/workspace/love/romantic-quotes.js;
    expires 1y;
    add_header Cache-Control "public, immutable";
}

location /love/modern-quotes-data.js {
    alias /root/workspace/love/modern-quotes-data.js;
    add_header Content-Type "application/javascript";
    expires 1y;
    add_header Cache-Control "public, immutable";
}

location /love/test-quotes.html {
    alias /root/workspace/love/test-quotes.html;
    add_header Content-Type "text/html";
}

location /love/test-modern-quotes.html {
    alias /root/workspace/love/test-modern-quotes.html;
    add_header Content-Type "text/html";
}

# Love site fonts
location /love/fonts/ {
    alias /root/workspace/love/fonts/;
    try_files $uri =404;

    # Set proper MIME types for fonts
    location ~* \.(ttf|otf|woff|woff2|eot)$ {
        add_header Access-Control-Allow-Origin "*";
        add_header Cache-Control "public, max-age=31536000, immutable";
        expires 1y;
    }
}

# Love site fallback backgrounds CSS
location /love/fallback-backgrounds.css {
    alias /root/workspace/love/fallback-backgrounds.css;
    expires 1y;
    add_header Cache-Control "public, immutable";
    add_header Content-Type "text/css";
}

# Love site video background handler JS
location /love/video-background-handler.js {
    alias /root/workspace/love/video-background-handler.js;
    expires 1y;
    add_header Cache-Control "public, immutable";
    add_header Content-Type "application/javascript";
}

# Love site background files (videos, images, etc.)
location /love/background/ {
    alias /root/workspace/love/background/;
    try_files $uri $uri/ =404;

    # Cache media files
    location ~* \.(mp4|webm|ogg|avi|mov|wmv|flv|mkv|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        # 支持大文件流式传输
        add_header Accept-Ranges bytes;
    }
}

# Love site API routes
location /love/api/ {
    # Remove /love prefix and pass to backend
    rewrite ^/love/api/(.*) /api/$1 break;
    
    proxy_pass http://love_backend;
    
    # Include proxy parameters
    include /root/workspace/shared/nginx/snippets/proxy-params.conf;
    
    # Include CORS headers for API
    include /root/workspace/shared/nginx/snippets/cors-headers.conf;
}

# Love site test files
location /love/test/ {
    alias /root/workspace/love/test/;
    try_files $uri $uri/ =404;
}
