# 项目配置文件说明

## 📁 配置文件列表

### 1. liangliangdamowang.edu.deal.conf
**主配置文件** - 完整的站点配置，包含所有三个项目的配置

- **用途**: 生产环境使用的完整配置文件
- **包含**: New-API、Love、GPT-Load三个项目的完整配置
- **部署**: 可直接复制到 `/etc/nginx/sites-available/` 或 `/etc/nginx/conf.d/`

### 2. new-api.conf
**New-API项目配置** - 独立的New-API项目配置

- **端口**: **********:3000 (Docker容器)
- **路径**: `/`, `/new-api/`, `/ai/`
- **功能**: AI API网关、前端界面、OpenAI兼容API

### 3. love.conf
**Love项目配置** - 独立的Love网站配置

- **端口**: 127.0.0.1:1314
- **路径**: `/love/`
- **功能**: 情侣网站、静态文件、API服务

### 4. gpt-load.conf
**GPT-Load项目配置** - 独立的GPT-Load代理服务配置

- **端口**: 127.0.0.1:3001
- **路径**: `/lgpt-load/`
- **功能**: AI接口代理、子路径部署、资源路径修复

## 🔧 使用方法

### 完整部署
```bash
# 复制主配置文件到nginx配置目录
cp liangliangdamowang.edu.deal.conf /etc/nginx/sites-available/
ln -s /etc/nginx/sites-available/liangliangdamowang.edu.deal.conf /etc/nginx/sites-enabled/

# 测试配置
nginx -t

# 重载nginx
systemctl reload nginx
```

### 模块化部署
```bash
# 在主nginx配置中包含项目配置
# 在server块中添加：
# include /root/workspace/shared/nginx/conf.d/love.conf;
# include /root/workspace/shared/nginx/conf.d/gpt-load.conf;
```

## 📋 配置特性

### 使用的Snippets
- `ssl-params.conf` - SSL/TLS安全配置
- `proxy-params.conf` - 标准代理参数
- `security-headers.conf` - 安全头配置
- `cors-headers.conf` - CORS跨域配置
- `cache-static.conf` - 静态文件缓存

### 端口映射
- **New-API**: **********:3000 → `/`, `/new-api/`, `/ai/`
- **Love**: 127.0.0.1:1314 → `/love/`
- **GPT-Load**: 127.0.0.1:3001 → `/lgpt-load/`

### 特殊功能
- **子路径部署**: GPT-Load支持完整的子路径部署
- **资源路径修复**: 自动修复HTML中的资源路径
- **WebSocket支持**: 所有项目都支持WebSocket连接
- **大文件上传**: GPT-Load支持100MB文件上传
- **静态文件缓存**: 优化的缓存策略

## 🔍 验证配置

```bash
# 测试配置语法
nginx -t -c /path/to/config.conf

# 检查配置文件
nginx -T | grep -A 10 -B 10 "server_name liangliangdamowang.edu.deal"
```

## 📝 维护说明

- 所有配置文件都使用snippets实现模块化
- 修改snippets会影响所有使用它的配置
- 新增项目时可参考现有配置文件的结构
- 配置变更后务必进行语法测试
