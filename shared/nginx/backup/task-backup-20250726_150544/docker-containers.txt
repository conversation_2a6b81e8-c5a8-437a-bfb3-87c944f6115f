CONTAINER ID   IMAGE                       COMMAND                  CREATED          STATUS                    PORTS                                     NAMES
4a8f024daf80   nginx:alpine                "/docker-entrypoint.…"   48 minutes ago   Up 28 minutes (healthy)   0.0.0.0:8080->80/tcp, [::]:8080->80/tcp   nginx-proxy
ca0abbeaabd1   calciumion/new-api:latest   "/one-api --log-dir …"   48 minutes ago   Up 40 minutes (healthy)   3000/tcp                                  new-api
32b3f26f59a3   mysql:8.2                   "docker-entrypoint.s…"   48 minutes ago   Up 40 minutes (healthy)   3306/tcp, 33060/tcp                       mysql
51b0e9aca4c9   redis:7-alpine              "docker-entrypoint.s…"   48 minutes ago   Up 40 minutes (healthy)   6379/tcp                                  redis
