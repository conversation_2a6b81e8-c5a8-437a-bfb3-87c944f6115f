# Complete Nginx configuration for liangliangdamowang.edu.deal
# This configuration includes New-API, Love, and GPT-Load projects
# Generated using modular configuration approach

# HTTP server - redirect to HTTPS
server {
    listen 80;
    server_name liangliangdamowang.edu.deal;
    
    # Redirect HTTP to HTTPS
    return 301 https://$server_name$request_uri;
}

# Upstream definitions
upstream new_api_backend {
    server **********:3000;  # Docker container IP (updated)
    keepalive 32;
}

upstream love_backend {
    server 127.0.0.1:1314;
    keepalive 32;
}

upstream gpt_load_backend {
    server 127.0.0.1:3001;
    keepalive 32;
}

# HTTPS server
server {
    listen 443 ssl http2;
    listen [::]:443 ssl http2;
    server_name liangliangdamowang.edu.deal;

    # SSL configuration
    ssl_certificate /etc/letsencrypt/live/liangliangdamowang.edu.deal/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/liangliangdamowang.edu.deal/privkey.pem;
    
    # Include SSL parameters
    include /root/workspace/shared/nginx/snippets/ssl-params.conf;
    
    # Include security headers
    include /root/workspace/shared/nginx/snippets/security-headers.conf;

    # ===== GPT-LOAD CONFIGURATIONS ===== ✨ GPT-Load AI代理服务
    
    # Handle /lgpt-load without trailing slash
    location = /lgpt-load {
        return 301 $scheme://$host/lgpt-load/;
    }
    
    # GPT-Load static assets (CSS, JS, images)
    location /lgpt-load/assets/ {
        # Remove /lgpt-load prefix before forwarding to backend
        rewrite ^/lgpt-load/(.*) /$1 break;
        
        # Forward to gpt-load backend
        proxy_pass http://gpt_load_backend;
        
        # Include proxy parameters
        include /root/workspace/shared/nginx/snippets/proxy-params.conf;
        
        # Cache static assets
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # GPT-Load API routes
    location /lgpt-load/api/ {
        # Remove /lgpt-load prefix before forwarding to backend
        rewrite ^/lgpt-load/(.*) /$1 break;
        
        # Forward to gpt-load backend
        proxy_pass http://gpt_load_backend;
        
        # Include proxy parameters
        include /root/workspace/shared/nginx/snippets/proxy-params.conf;
        
        # Include CORS headers for API
        include /root/workspace/shared/nginx/snippets/cors-headers.conf;
        
        # Handle large request bodies
        client_max_body_size 100M;
    }
    
    # GPT-Load proxy routes
    location /lgpt-load/proxy/ {
        # Remove /lgpt-load prefix before forwarding to backend
        rewrite ^/lgpt-load/(.*) /$1 break;
        
        # Forward to gpt-load backend
        proxy_pass http://gpt_load_backend;
        
        # Include proxy parameters
        include /root/workspace/shared/nginx/snippets/proxy-params.conf;
        
        # Include CORS headers for API
        include /root/workspace/shared/nginx/snippets/cors-headers.conf;
        
        # Handle large request bodies
        client_max_body_size 100M;
    }
    
    # GPT-Load health check
    location /lgpt-load/health {
        # Remove /lgpt-load prefix before forwarding to backend
        rewrite ^/lgpt-load/(.*) /$1 break;
        
        # Forward to gpt-load backend
        proxy_pass http://gpt_load_backend;
        
        # Include proxy parameters
        include /root/workspace/shared/nginx/snippets/proxy-params.conf;
        
        access_log off;
    }
    
    # GPT-Load main interface
    location /lgpt-load/ {
        # Remove /lgpt-load prefix before forwarding to backend
        rewrite ^/lgpt-load/(.*) /$1 break;
        
        # Forward to gpt-load backend
        proxy_pass http://gpt_load_backend;
        
        # Include proxy parameters
        include /root/workspace/shared/nginx/snippets/proxy-params.conf;
        
        # Handle large request bodies
        client_max_body_size 100M;
        
        # Fix asset paths in HTML for subpath deployment
        sub_filter 'href="/assets/' 'href="/lgpt-load/assets/';
        sub_filter 'src="/assets/' 'src="/lgpt-load/assets/';
        sub_filter '"/api/' '"/lgpt-load/api/';
        sub_filter "'/api/" "'/lgpt-load/api/";
        sub_filter '"/proxy/' '"/lgpt-load/proxy/';
        sub_filter "'/proxy/" "'/lgpt-load/proxy/";
        sub_filter '"/health' '"/lgpt-load/health';
        sub_filter "'/health" "'/lgpt-load/health";
        sub_filter_once off;
        
        # Security headers for GPT-Load
        add_header X-Frame-Options "SAMEORIGIN" always;
        add_header X-Content-Type-Options "nosniff" always;
    }

    # ===== GPT-LOAD ALTERNATIVE PATH ===== ✨ 支持 /gpt-load/ 路径访问

    # Handle /gpt-load without trailing slash - redirect to /lgpt-load/
    location = /gpt-load {
        return 301 $scheme://$host/lgpt-load/;
    }

    # Handle /gpt-load/ with trailing slash - redirect to /lgpt-load/
    location /gpt-load/ {
        return 301 $scheme://$host/lgpt-load/;
    }

    # ===== LOVE SITE CONFIGURATIONS =====
    
    # Handle /love without trailing slash
    location = /love {
        return 301 $scheme://$host/love/;
    }
    
    # Love site main page
    location = /love/ {
        alias /root/workspace/love/html/;
        try_files /index.html =404;
    }

    # Love site clean URLs (without .html extension)
    location ~ ^/love/(together-days|anniversary|meetings|memorial)$ {
        alias /root/workspace/love/html/;
        try_files /$1.html =404;
    }

    # Love site HTML files (direct access)
    location /love/html/ {
        alias /root/workspace/love/html/;
        try_files $uri $uri/ =404;

        # Include static file caching
        include /root/workspace/shared/nginx/snippets/cache-static.conf;
    }

    # Love site root files (CSS, JS, etc.)
    location /love/style.css {
        alias /root/workspace/love/style.css;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    location /love/pages.css {
        alias /root/workspace/love/pages.css;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    location /love/script.js {
        alias /root/workspace/love/script.js;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    location /love/romantic-quotes.js {
        alias /root/workspace/love/romantic-quotes.js;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    location /love/modern-quotes-data.js {
        alias /root/workspace/love/modern-quotes-data.js;
        add_header Content-Type "application/javascript";
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    location /love/test-quotes.html {
        alias /root/workspace/love/test-quotes.html;
        add_header Content-Type "text/html";
    }

    location /love/test-modern-quotes.html {
        alias /root/workspace/love/test-modern-quotes.html;
        add_header Content-Type "text/html";
    }

    # Love site fonts
    location /love/fonts/ {
        alias /root/workspace/love/fonts/;
        try_files $uri =404;

        # Set proper MIME types for fonts
        location ~* \.(ttf|otf|woff|woff2|eot)$ {
            add_header Access-Control-Allow-Origin "*";
            add_header Cache-Control "public, max-age=31536000, immutable";
            expires 1y;
        }
    }

    # Love site fallback backgrounds CSS
    location /love/fallback-backgrounds.css {
        alias /root/workspace/love/fallback-backgrounds.css;
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header Content-Type "text/css";
    }

    # Love site video background handler JS
    location /love/video-background-handler.js {
        alias /root/workspace/love/video-background-handler.js;
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header Content-Type "application/javascript";
    }

    # Love site background files (videos, images, etc.)
    location /love/background/ {
        alias /root/workspace/love/background/;
        try_files $uri $uri/ =404;

        # Cache media files
        location ~* \.(mp4|webm|ogg|avi|mov|wmv|flv|mkv|png|jpg|jpeg|gif|ico|svg)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
            # 支持大文件流式传输
            add_header Accept-Ranges bytes;
        }
    }

    # Love site API routes
    location /love/api/ {
        # Remove /love prefix and pass to backend
        rewrite ^/love/api/(.*) /api/$1 break;

        proxy_pass http://love_backend;

        # Include proxy parameters
        include /root/workspace/shared/nginx/snippets/proxy-params.conf;

        # Include CORS headers for API
        include /root/workspace/shared/nginx/snippets/cors-headers.conf;
    }

    # Love site test files
    location /love/test/ {
        alias /root/workspace/love/test/;
        try_files $uri $uri/ =404;
    }

    # ===== AI PATH CONFIGURATIONS ===== ✨ 新增AI路径功能

    # AI path static files (前端资源)
    location /ai/assets/ {
        alias /root/workspace/new-api/web/dist/assets/;
        try_files $uri $uri/ =404;

        # Include static file caching
        include /root/workspace/shared/nginx/snippets/cache-static.conf;
    }

    # AI path frontend (前端页面)
    location /ai/ {
        alias /root/workspace/new-api/web/dist/;
        try_files $uri $uri/ /index.html;

        # No cache for HTML files
        location ~* \.html$ {
            expires -1;
            add_header Cache-Control "no-cache, no-store, must-revalidate";
            add_header Pragma "no-cache";
        }
    }

    # AI path API routes
    location /ai/api/ {
        # Remove /ai prefix before forwarding to backend
        rewrite ^/ai/(.*) /$1 break;

        # Forward to new-api backend
        proxy_pass http://new_api_backend;

        # Include proxy parameters
        include /root/workspace/shared/nginx/snippets/proxy-params.conf;

        # Add custom header to indicate the original path prefix
        proxy_set_header X-Original-Path-Prefix /ai;
    }

    # AI path OpenAI API routes
    location /ai/v1/ {
        # Remove /ai prefix before forwarding to backend
        rewrite ^/ai/(.*) /$1 break;

        # Forward to new-api backend
        proxy_pass http://new_api_backend;

        # Include proxy parameters
        include /root/workspace/shared/nginx/snippets/proxy-params.conf;

        # Add custom header to indicate the original path prefix
        proxy_set_header X-Original-Path-Prefix /ai;
    }

    # Handle /ai without trailing slash
    location = /ai {
        return 301 $scheme://$host/ai/;
    }

    # New API service at /new-api path (for backward compatibility)
    location /new-api/ {
        # Remove /new-api prefix before forwarding to backend
        rewrite ^/new-api/(.*) /$1 break;

        # Forward to new-api backend
        proxy_pass http://new_api_backend;

        # Include proxy parameters
        include /root/workspace/shared/nginx/snippets/proxy-params.conf;

        # Add custom header to indicate the original path prefix
        proxy_set_header X-Original-Path-Prefix /new-api;
    }

    # Handle /new-api without trailing slash
    location = /new-api {
        return 301 $scheme://$host/new-api/;
    }

    # Main site root - New API service (direct access)
    location / {
        # Forward to new-api backend
        proxy_pass http://new_api_backend;

        # Include proxy parameters
        include /root/workspace/shared/nginx/snippets/proxy-params.conf;
    }

    # Health check endpoint
    location /health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }

    # Error pages
    error_page 404 /404.html;
    error_page 500 502 503 504 /50x.html;

    location = /50x.html {
        root /usr/share/nginx/html;
    }
}
